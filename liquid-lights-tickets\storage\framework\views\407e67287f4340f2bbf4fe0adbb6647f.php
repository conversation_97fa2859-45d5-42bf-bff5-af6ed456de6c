<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['meta' => [], 'structuredData' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['meta' => [], 'structuredData' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $defaultMeta = [
        'title' => 'Liquid Lights - Premium Nightlife Tickets',
        'description' => 'Experience the ultimate nightlife with premium events, top DJs, and unforgettable moments.',
        'keywords' => ['nightlife', 'events', 'tickets', 'concerts', 'parties'],
        'image' => asset('images/og-default.jpg'),
        'url' => url()->current(),
        'type' => 'website',
        'site_name' => 'Liquid Lights',
        'locale' => 'en_US',
        'twitter_card' => 'summary_large_image',
        'twitter_site' => '@liquidlights',
    ];
    
    $meta = array_merge($defaultMeta, $meta);
    $keywords = is_array($meta['keywords']) ? implode(', ', $meta['keywords']) : $meta['keywords'];
?>

<!-- Basic Meta Tags -->
<title><?php echo e($meta['title']); ?></title>
<meta name="description" content="<?php echo e($meta['description']); ?>">
<meta name="keywords" content="<?php echo e($keywords); ?>">
<meta name="author" content="Liquid Lights">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta charset="UTF-8">

<!-- Canonical URL -->
<link rel="canonical" href="<?php echo e($meta['url']); ?>">

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="<?php echo e($meta['title']); ?>">
<meta property="og:description" content="<?php echo e($meta['description']); ?>">
<meta property="og:image" content="<?php echo e($meta['image']); ?>">
<meta property="og:url" content="<?php echo e($meta['url']); ?>">
<meta property="og:type" content="<?php echo e($meta['type']); ?>">
<meta property="og:site_name" content="<?php echo e($meta['site_name']); ?>">
<meta property="og:locale" content="<?php echo e($meta['locale']); ?>">

<?php if(isset($meta['article_published_time'])): ?>
    <meta property="article:published_time" content="<?php echo e($meta['article_published_time']); ?>">
<?php endif; ?>

<?php if(isset($meta['article_modified_time'])): ?>
    <meta property="article:modified_time" content="<?php echo e($meta['article_modified_time']); ?>">
<?php endif; ?>

<?php if(isset($meta['article_author'])): ?>
    <meta property="article:author" content="<?php echo e($meta['article_author']); ?>">
<?php endif; ?>

<?php if(isset($meta['article_section'])): ?>
    <meta property="article:section" content="<?php echo e($meta['article_section']); ?>">
<?php endif; ?>

<?php if(isset($meta['article_tag']) && is_array($meta['article_tag'])): ?>
    <?php $__currentLoopData = $meta['article_tag']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <meta property="article:tag" content="<?php echo e($tag); ?>">
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="<?php echo e($meta['twitter_card']); ?>">
<meta name="twitter:site" content="<?php echo e($meta['twitter_site']); ?>">
<meta name="twitter:title" content="<?php echo e($meta['title']); ?>">
<meta name="twitter:description" content="<?php echo e($meta['description']); ?>">
<meta name="twitter:image" content="<?php echo e($meta['image']); ?>">
<meta name="twitter:url" content="<?php echo e($meta['url']); ?>">

<!-- Additional SEO Meta Tags -->
<meta name="theme-color" content="#0a0a0a">
<meta name="msapplication-TileColor" content="#0a0a0a">
<meta name="application-name" content="Liquid Lights">

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
<link rel="manifest" href="/site.webmanifest">

<!-- Structured Data (JSON-LD) -->
<?php if($structuredData): ?>
    <script type="application/ld+json">
        <?php echo json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>

    </script>
<?php endif; ?>

<!-- Additional Structured Data for Organization -->
<script type="application/ld+json">
{
    "<?php $__contextArgs = [];
if (context()->has($__contextArgs[0])) :
if (isset($value)) { $__contextPrevious[] = $value; }
$value = context()->get($__contextArgs[0]); ?>": "https://schema.org",
    "@type": "Organization",
    "name": "Liquid Lights",
    "url": "<?php echo e(url('/')); ?>",
    "logo": "<?php echo e(asset('images/logo.png')); ?>",
    "description": "Premium nightlife events and ticket booking platform",
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "Mumbai",
        "addressRegion": "Maharashtra",
        "addressCountry": "IN"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+91-98765-43210",
        "contactType": "customer service",
        "email": "<EMAIL>"
    },
    "sameAs": [
        "https://www.facebook.com/liquidlights",
        "https://www.instagram.com/liquidlights",
        "https://www.twitter.com/liquidlights"
    ]
}
</script>

<!-- Website Structured Data -->
<script type="application/ld+json">
{
    "<?php $__contextArgs = [];
if (context()->has($__contextArgs[0])) :
if (isset($value)) { $__contextPrevious[] = $value; }
$value = context()->get($__contextArgs[0]); ?>": "https://schema.org",
    "@type": "WebSite",
    "name": "Liquid Lights",
    "url": "<?php echo e(url('/')); ?>",
    "description": "Premium nightlife events and ticket booking platform",
    "potentialAction": {
        "@type": "SearchAction",
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": "<?php echo e(route('public.events')); ?>?search={search_term_string}"
        },
        "query-input": "required name=search_term_string"
    }
}
</script>
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\LLe\liquid-lights-tickets\resources\views/components/meta-tags.blade.php ENDPATH**/ ?>