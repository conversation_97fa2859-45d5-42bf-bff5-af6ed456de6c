<?php

namespace Laravel\Pennant\Events;

use Illuminate\Queue\SerializesModels;

class FeatureUpdatedForAllScopes
{
    use SerializesModels;

    /**
     * The feature name.
     *
     * @var string
     */
    public $feature;

    /**
     * The new feature value.
     *
     * @var mixed
     */
    public $value;

    /**
     * Create a new event instance.
     *
     * @param  string  $feature
     * @param  mixed  $value
     */
    public function __construct($feature, $value)
    {
        $this->feature = $feature;
        $this->value = $value;
    }
}
