<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class SettingController extends Controller
{
    /**
     * Display settings grouped by category
     */
    public function index()
    {
        $settingsGroups = Setting::getAllGrouped();
        return view('admin.settings.index', compact('settingsGroups'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $settings = $request->input('settings', []);

        foreach ($settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();

            if ($setting) {
                // Handle file uploads
                if ($setting->type === 'file' && $request->hasFile("settings.{$key}")) {
                    // Delete old file
                    if ($setting->value && Storage::disk('public')->exists($setting->value)) {
                        Storage::disk('public')->delete($setting->value);
                    }

                    $value = $request->file("settings.{$key}")->store('settings', 'public');
                }

                $setting->update(['value' => $value]);
            }
        }

        // Clear cache
        Cache::forget('settings');
        Cache::forget('public_settings');

        return redirect()->route('admin.settings.index')
                        ->with('success', 'Settings updated successfully.');
    }

    /**
     * Create a new setting
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'key' => 'required|string|unique:settings,key',
            'value' => 'nullable|string',
            'type' => 'required|in:string,integer,boolean,json,file',
            'group' => 'required|string',
            'label' => 'required|string',
            'description' => 'nullable|string',
            'options' => 'nullable|array',
            'is_public' => 'boolean',
            'is_required' => 'boolean',
            'validation_rules' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        ]);

        Setting::create($validated);

        return redirect()->route('admin.settings.index')
                        ->with('success', 'Setting created successfully.');
    }

    /**
     * Delete a setting
     */
    public function destroy(Setting $setting)
    {
        // Delete file if it's a file setting
        if ($setting->type === 'file' && $setting->value && Storage::disk('public')->exists($setting->value)) {
            Storage::disk('public')->delete($setting->value);
        }

        $setting->delete();

        return redirect()->route('admin.settings.index')
                        ->with('success', 'Setting deleted successfully.');
    }

    /**
     * Reset settings to default
     */
    public function reset(Request $request)
    {
        $group = $request->input('group');

        if ($group) {
            Setting::where('group', $group)->delete();
            $this->seedDefaultSettings($group);
        }

        return redirect()->route('admin.settings.index')
                        ->with('success', 'Settings reset to default successfully.');
    }

    /**
     * Export settings
     */
    public function export()
    {
        $settings = Setting::all()->toArray();

        $filename = 'settings_' . date('Y-m-d_H-i-s') . '.json';

        return response()->json($settings)
                        ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Import settings
     */
    public function import(Request $request)
    {
        $request->validate([
            'settings_file' => 'required|file|mimes:json'
        ]);

        $content = file_get_contents($request->file('settings_file')->getRealPath());
        $settings = json_decode($content, true);

        if (!$settings) {
            return back()->with('error', 'Invalid settings file.');
        }

        foreach ($settings as $settingData) {
            Setting::updateOrCreate(
                ['key' => $settingData['key']],
                $settingData
            );
        }

        return redirect()->route('admin.settings.index')
                        ->with('success', 'Settings imported successfully.');
    }

    /**
     * Seed default settings for a group
     */
    private function seedDefaultSettings($group = null)
    {
        $defaultSettings = $this->getDefaultSettings();

        foreach ($defaultSettings as $setting) {
            if (!$group || $setting['group'] === $group) {
                Setting::updateOrCreate(
                    ['key' => $setting['key']],
                    $setting
                );
            }
        }
    }

    /**
     * Get default settings
     */
    private function getDefaultSettings()
    {
        return [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Liquid Lights Tickets',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'is_public' => true,
                'is_required' => true,
                'sort_order' => 1
            ],
            [
                'key' => 'site_description',
                'value' => 'Premium nightlife and entertainment ticketing platform',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Site Description',
                'description' => 'A brief description of your website',
                'is_public' => true,
                'sort_order' => 2
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Contact Email',
                'description' => 'Primary contact email address',
                'is_public' => true,
                'validation_rules' => 'email',
                'sort_order' => 3
            ],
            [
                'key' => 'contact_phone',
                'value' => '+91 98765 43210',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Contact Phone',
                'description' => 'Primary contact phone number',
                'is_public' => true,
                'sort_order' => 4
            ],
            // Appearance Settings
            [
                'key' => 'logo',
                'value' => null,
                'type' => 'file',
                'group' => 'appearance',
                'label' => 'Site Logo',
                'description' => 'Upload your site logo',
                'is_public' => true,
                'sort_order' => 1
            ],
            [
                'key' => 'favicon',
                'value' => null,
                'type' => 'file',
                'group' => 'appearance',
                'label' => 'Favicon',
                'description' => 'Upload your site favicon',
                'is_public' => true,
                'sort_order' => 2
            ],
            [
                'key' => 'primary_color',
                'value' => '#3B82F6',
                'type' => 'string',
                'group' => 'appearance',
                'label' => 'Primary Color',
                'description' => 'Primary brand color',
                'is_public' => true,
                'sort_order' => 3
            ],
            // Payment Settings
            [
                'key' => 'razorpay_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'payment',
                'label' => 'Enable Razorpay',
                'description' => 'Enable Razorpay payment gateway',
                'sort_order' => 1
            ],
            [
                'key' => 'stripe_enabled',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'payment',
                'label' => 'Enable Stripe',
                'description' => 'Enable Stripe payment gateway',
                'sort_order' => 2
            ],
            // Notification Settings
            [
                'key' => 'email_notifications',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'notification',
                'label' => 'Email Notifications',
                'description' => 'Enable email notifications',
                'sort_order' => 1
            ],
            [
                'key' => 'sms_notifications',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'notification',
                'label' => 'SMS Notifications',
                'description' => 'Enable SMS notifications',
                'sort_order' => 2
            ],
        ];
    }
}
