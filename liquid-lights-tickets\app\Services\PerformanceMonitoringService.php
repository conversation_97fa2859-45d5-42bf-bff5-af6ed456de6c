<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Features;

class PerformanceMonitoringService
{
    protected array $metrics = [];
    protected float $startTime;
    protected int $startMemory;

    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
    }

    /**
     * Start monitoring a specific operation
     */
    public function startTimer(string $operation): void
    {
        $this->metrics[$operation] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
        ];
    }

    /**
     * End monitoring and record metrics
     */
    public function endTimer(string $operation): array
    {
        if (!isset($this->metrics[$operation])) {
            return [];
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        $metrics = [
            'operation' => $operation,
            'duration' => round(($endTime - $this->metrics[$operation]['start_time']) * 1000, 2), // ms
            'memory_used' => $endMemory - $this->metrics[$operation]['start_memory'],
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => now(),
        ];

        // Store metrics if performance monitoring is enabled
        if (Features::enabled('performance-monitoring')) {
            $this->storeMetrics($metrics);
        }

        return $metrics;
    }

    /**
     * Monitor database query performance
     */
    public function monitorDatabaseQueries(): void
    {
        if (!Features::enabled('performance-monitoring')) {
            return;
        }

        DB::listen(function ($query) {
            $metrics = [
                'type' => 'database_query',
                'sql' => $query->sql,
                'bindings' => $query->bindings,
                'duration' => $query->time,
                'connection' => $query->connectionName,
                'timestamp' => now(),
            ];

            // Log slow queries
            if ($query->time > 1000) { // Queries taking more than 1 second
                Log::warning('Slow database query detected', $metrics);
            }

            $this->storeMetrics($metrics);
        });
    }

    /**
     * Monitor cache performance
     */
    public function monitorCachePerformance(): array
    {
        $cacheService = app(CacheOptimizationService::class);
        return $cacheService->getCacheStats();
    }

    /**
     * Get page load performance metrics
     */
    public function getPageLoadMetrics(): array
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        return [
            'total_duration' => round(($endTime - $this->startTime) * 1000, 2), // ms
            'memory_used' => $endMemory - $this->startMemory,
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit'),
            'queries_count' => count(DB::getQueryLog()),
            'timestamp' => now(),
        ];
    }

    /**
     * Store performance metrics
     */
    protected function storeMetrics(array $metrics): void
    {
        // Store in cache for recent metrics
        $key = 'performance_metrics:' . date('Y-m-d-H');
        $existingMetrics = Cache::get($key, []);
        $existingMetrics[] = $metrics;
        
        // Keep only last 1000 metrics per hour
        if (count($existingMetrics) > 1000) {
            $existingMetrics = array_slice($existingMetrics, -1000);
        }
        
        Cache::put($key, $existingMetrics, 3600); // Store for 1 hour
    }

    /**
     * Get performance report
     */
    public function getPerformanceReport(int $hours = 24): array
    {
        $report = [
            'summary' => [
                'total_requests' => 0,
                'avg_response_time' => 0,
                'slow_requests' => 0,
                'database_queries' => 0,
                'slow_queries' => 0,
            ],
            'hourly_breakdown' => [],
            'slow_operations' => [],
            'memory_usage' => [],
        ];

        // Collect metrics from the last N hours
        for ($i = 0; $i < $hours; $i++) {
            $hour = now()->subHours($i)->format('Y-m-d-H');
            $key = 'performance_metrics:' . $hour;
            $metrics = Cache::get($key, []);

            if (!empty($metrics)) {
                $hourlyStats = $this->analyzeHourlyMetrics($metrics);
                $report['hourly_breakdown'][$hour] = $hourlyStats;
                
                // Update summary
                $report['summary']['total_requests'] += $hourlyStats['requests'];
                $report['summary']['database_queries'] += $hourlyStats['db_queries'];
                $report['summary']['slow_queries'] += $hourlyStats['slow_queries'];
                
                if ($hourlyStats['avg_duration'] > 2000) { // > 2 seconds
                    $report['summary']['slow_requests']++;
                }
            }
        }

        // Calculate averages
        if ($report['summary']['total_requests'] > 0) {
            $totalDuration = array_sum(array_column($report['hourly_breakdown'], 'total_duration'));
            $report['summary']['avg_response_time'] = round($totalDuration / $report['summary']['total_requests'], 2);
        }

        return $report;
    }

    /**
     * Analyze metrics for a specific hour
     */
    protected function analyzeHourlyMetrics(array $metrics): array
    {
        $stats = [
            'requests' => 0,
            'db_queries' => 0,
            'slow_queries' => 0,
            'total_duration' => 0,
            'avg_duration' => 0,
            'max_duration' => 0,
            'memory_peak' => 0,
        ];

        foreach ($metrics as $metric) {
            if ($metric['type'] === 'database_query') {
                $stats['db_queries']++;
                if ($metric['duration'] > 1000) {
                    $stats['slow_queries']++;
                }
            } else {
                $stats['requests']++;
                $stats['total_duration'] += $metric['duration'];
                $stats['max_duration'] = max($stats['max_duration'], $metric['duration']);
                $stats['memory_peak'] = max($stats['memory_peak'], $metric['memory_peak'] ?? 0);
            }
        }

        if ($stats['requests'] > 0) {
            $stats['avg_duration'] = round($stats['total_duration'] / $stats['requests'], 2);
        }

        return $stats;
    }

    /**
     * Get real-time performance metrics
     */
    public function getRealTimeMetrics(): array
    {
        return [
            'current_memory' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'memory_limit' => $this->parseMemoryLimit(ini_get('memory_limit')),
            'memory_percentage' => round((memory_get_usage(true) / $this->parseMemoryLimit(ini_get('memory_limit'))) * 100, 2),
            'active_connections' => $this->getActiveConnections(),
            'cache_stats' => $this->monitorCachePerformance(),
            'load_average' => $this->getSystemLoad(),
        ];
    }

    /**
     * Parse memory limit string to bytes
     */
    protected function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Get active database connections
     */
    protected function getActiveConnections(): int
    {
        try {
            $result = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            return (int) ($result[0]->Value ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get system load average
     */
    protected function getSystemLoad(): array
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => round($load[0], 2),
                '5min' => round($load[1], 2),
                '15min' => round($load[2], 2),
            ];
        }

        return ['1min' => 0, '5min' => 0, '15min' => 0];
    }

    /**
     * Check if system is under high load
     */
    public function isHighLoad(): bool
    {
        $metrics = $this->getRealTimeMetrics();
        
        return $metrics['memory_percentage'] > 80 || 
               $metrics['load_average']['1min'] > 2.0 ||
               $metrics['active_connections'] > 100;
    }

    /**
     * Get performance recommendations
     */
    public function getPerformanceRecommendations(): array
    {
        $recommendations = [];
        $metrics = $this->getRealTimeMetrics();
        $report = $this->getPerformanceReport(24);

        // Memory recommendations
        if ($metrics['memory_percentage'] > 80) {
            $recommendations[] = [
                'type' => 'memory',
                'severity' => 'high',
                'message' => 'Memory usage is high (' . $metrics['memory_percentage'] . '%). Consider optimizing queries or increasing memory limit.',
            ];
        }

        // Response time recommendations
        if ($report['summary']['avg_response_time'] > 2000) {
            $recommendations[] = [
                'type' => 'response_time',
                'severity' => 'medium',
                'message' => 'Average response time is slow (' . $report['summary']['avg_response_time'] . 'ms). Consider enabling caching or optimizing database queries.',
            ];
        }

        // Database recommendations
        if ($report['summary']['slow_queries'] > 10) {
            $recommendations[] = [
                'type' => 'database',
                'severity' => 'medium',
                'message' => 'Multiple slow database queries detected. Consider adding indexes or optimizing queries.',
            ];
        }

        // Cache recommendations
        if (!Features::enabled('caching-enhanced')) {
            $recommendations[] = [
                'type' => 'caching',
                'severity' => 'low',
                'message' => 'Enhanced caching is disabled. Enable it to improve performance.',
            ];
        }

        return $recommendations;
    }

    /**
     * Clear old performance metrics
     */
    public function clearOldMetrics(int $hoursToKeep = 168): void // Default: keep 1 week
    {
        $cutoffTime = now()->subHours($hoursToKeep);
        
        for ($i = $hoursToKeep; $i < 720; $i++) { // Check up to 30 days back
            $hour = now()->subHours($i)->format('Y-m-d-H');
            $key = 'performance_metrics:' . $hour;
            Cache::forget($key);
        }
    }
}
