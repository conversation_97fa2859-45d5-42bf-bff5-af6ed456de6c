<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'options',
        'is_public',
        'is_required',
        'validation_rules',
        'sort_order'
    ];

    protected $casts = [
        'options' => 'array',
        'is_public' => 'boolean',
        'is_required' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when settings are modified
        static::saved(function () {
            Cache::forget('settings');
            Cache::forget('public_settings');
        });

        static::deleted(function () {
            Cache::forget('settings');
            Cache::forget('public_settings');
        });
    }

    /**
     * Scopes
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('label');
    }

    /**
     * Accessors
     */
    public function getValueAttribute($value)
    {
        return $this->castValue($value, $this->type);
    }

    /**
     * Mutators
     */
    public function setValueAttribute($value)
    {
        $this->attributes['value'] = $this->prepareValue($value, $this->type);
    }

    /**
     * Methods
     */
    protected function castValue($value, $type)
    {
        if ($value === null) {
            return null;
        }

        return match($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json' => json_decode($value, true),
            'array' => is_array($value) ? $value : json_decode($value, true),
            default => $value
        };
    }

    protected function prepareValue($value, $type)
    {
        return match($type) {
            'boolean' => $value ? '1' : '0',
            'json', 'array' => is_string($value) ? $value : json_encode($value),
            default => (string) $value
        };
    }

    /**
     * Static methods for easy access
     */
    public static function get($key, $default = null)
    {
        $settings = Cache::remember('settings', 3600, function () {
            return static::pluck('value', 'key')->toArray();
        });

        $setting = static::where('key', $key)->first();
        if (!$setting) {
            return $default;
        }

        return $setting->castValue($settings[$key] ?? $default, $setting->type);
    }

    public static function set($key, $value)
    {
        $setting = static::where('key', $key)->first();

        if ($setting) {
            $setting->update(['value' => $value]);
        } else {
            static::create([
                'key' => $key,
                'value' => $value,
                'type' => 'string',
                'group' => 'general',
                'label' => ucfirst(str_replace('_', ' ', $key))
            ]);
        }

        return $value;
    }

    public static function getPublic()
    {
        return Cache::remember('public_settings', 3600, function () {
            return static::public()->pluck('value', 'key')->toArray();
        });
    }

    public static function getByGroup($group)
    {
        return static::byGroup($group)->ordered()->get();
    }

    public static function getAllGrouped()
    {
        return static::ordered()->get()->groupBy('group');
    }
}
