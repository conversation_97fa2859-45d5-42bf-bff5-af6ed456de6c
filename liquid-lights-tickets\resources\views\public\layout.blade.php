<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Liquid Lights - Premium Nightlife Tickets')</title>
    <meta name="description" content="@yield('description', 'Book tickets for the hottest nightlife events, concerts, and entertainment experiences. Premium venues, top DJs, and unforgettable nights.')">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/public.js'])

    <!-- Modern Dark Theme with Neon Accents -->
    <style>
        :root {
            /* Dark Theme Colors */
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;
            --bg-card: #1e1e1e;
            
            /* Neon Colors */
            --neon-blue: #00d4ff;
            --neon-purple: #8b5cf6;
            --neon-pink: #f472b6;
            --neon-green: #10b981;
            
            /* Text Colors */
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            
            /* Gradients */
            --gradient-primary: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
            --gradient-secondary: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
        }

        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
        }

        .font-display {
            font-family: 'Playfair Display', serif;
        }

        /* Neon Text Effects */
        .neon-text {
            color: var(--neon-blue);
            text-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
        }

        .neon-text-purple {
            color: var(--neon-purple);
            text-shadow: 0 0 10px var(--neon-purple), 0 0 20px var(--neon-purple), 0 0 30px var(--neon-purple);
        }

        .neon-text-pink {
            color: var(--neon-pink);
            text-shadow: 0 0 10px var(--neon-pink), 0 0 20px var(--neon-pink), 0 0 30px var(--neon-pink);
        }

        /* Glass Morphism */
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
        }

        /* Gradient Buttons */
        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }

        .btn-secondary {
            background: var(--gradient-secondary);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
        }

        /* Navigation */
        .navbar {
            background: rgba(10, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Cards */
        .event-card {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .event-card:hover {
            transform: translateY(-5px);
            border-color: var(--neon-blue);
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes pulse-neon {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .pulse-neon {
            animation: pulse-neon 2s ease-in-out infinite;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .neon-text {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Navigation -->
    <nav class="navbar fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{{ route('public.index') }}" class="flex items-center">
                        <span class="text-2xl font-bold font-display neon-text">Liquid</span>
                        <span class="text-2xl font-bold font-display neon-text-pink ml-1">Lights</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="{{ route('public.index') }}" 
                           class="text-white hover:text-blue-400 px-3 py-2 text-sm font-medium transition-colors">
                            Home
                        </a>
                        <a href="{{ route('public.events') }}" 
                           class="text-white hover:text-blue-400 px-3 py-2 text-sm font-medium transition-colors">
                            Events
                        </a>
                        <a href="/about-us" 
                           class="text-white hover:text-blue-400 px-3 py-2 text-sm font-medium transition-colors">
                            About
                        </a>
                        <a href="/contact-us" 
                           class="text-white hover:text-blue-400 px-3 py-2 text-sm font-medium transition-colors">
                            Contact
                        </a>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6 space-x-4">
                        @auth
                            <a href="{{ route('user.dashboard') }}" class="btn-primary">
                                Dashboard
                            </a>
                            <form method="POST" action="{{ route('logout') }}" class="inline">
                                @csrf
                                <button type="submit" class="text-gray-300 hover:text-white text-sm">
                                    Logout
                                </button>
                            </form>
                        @else
                            <a href="{{ route('login') }}" class="text-white hover:text-blue-400 text-sm font-medium">
                                Login
                            </a>
                            <a href="{{ route('register') }}" class="btn-primary">
                                Sign Up
                            </a>
                        @endauth
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" 
                            class="text-gray-400 hover:text-white focus:outline-none focus:text-white"
                            onclick="toggleMobileMenu()">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 bg-gray-900 border-t border-gray-700">
                <a href="{{ route('public.index') }}" 
                   class="text-white hover:text-blue-400 block px-3 py-2 text-base font-medium">
                    Home
                </a>
                <a href="{{ route('public.events') }}" 
                   class="text-white hover:text-blue-400 block px-3 py-2 text-base font-medium">
                    Events
                </a>
                <a href="/about-us" 
                   class="text-white hover:text-blue-400 block px-3 py-2 text-base font-medium">
                    About
                </a>
                <a href="/contact-us" 
                   class="text-white hover:text-blue-400 block px-3 py-2 text-base font-medium">
                    Contact
                </a>
                
                @auth
                    <a href="{{ route('user.dashboard') }}" 
                       class="text-white hover:text-blue-400 block px-3 py-2 text-base font-medium">
                        Dashboard
                    </a>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" 
                                class="text-white hover:text-blue-400 block px-3 py-2 text-base font-medium w-full text-left">
                            Logout
                        </button>
                    </form>
                @else
                    <a href="{{ route('login') }}" 
                       class="text-white hover:text-blue-400 block px-3 py-2 text-base font-medium">
                        Login
                    </a>
                    <a href="{{ route('register') }}" 
                       class="text-white hover:text-blue-400 block px-3 py-2 text-base font-medium">
                        Sign Up
                    </a>
                @endauth
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 border-t border-gray-800">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <span class="text-2xl font-bold font-display neon-text">Liquid</span>
                        <span class="text-2xl font-bold font-display neon-text-pink ml-1">Lights</span>
                    </div>
                    <p class="text-gray-400 text-sm max-w-md">
                        Experience the ultimate nightlife with premium events, top DJs, and unforgettable moments. 
                        Your gateway to the best entertainment in the city.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-white font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('public.events') }}" class="text-gray-400 hover:text-white text-sm">Events</a></li>
                        <li><a href="/about-us" class="text-gray-400 hover:text-white text-sm">About Us</a></li>
                        <li><a href="/contact-us" class="text-gray-400 hover:text-white text-sm">Contact</a></li>
                        <li><a href="/privacy-policy" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a></li>
                    </ul>
                </div>

                <!-- Contact -->
                <div>
                    <h3 class="text-white font-semibold mb-4">Contact</h3>
                    <ul class="space-y-2">
                        <li class="text-gray-400 text-sm"><EMAIL></li>
                        <li class="text-gray-400 text-sm">+91 98765 43210</li>
                        <li class="text-gray-400 text-sm">Mumbai, Maharashtra</li>
                    </ul>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-800">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        © {{ date('Y') }} Liquid Lights. All rights reserved.
                    </p>
                    <div class="flex space-x-6 mt-4 md:mt-0">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">Facebook</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">Instagram</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">Twitter</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }
    </script>
</body>
</html>
