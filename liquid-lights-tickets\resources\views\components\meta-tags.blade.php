@props(['meta' => [], 'structuredData' => null])

@php
    $defaultMeta = [
        'title' => 'Liquid Lights - Premium Nightlife Tickets',
        'description' => 'Experience the ultimate nightlife with premium events, top DJs, and unforgettable moments.',
        'keywords' => ['nightlife', 'events', 'tickets', 'concerts', 'parties'],
        'image' => asset('images/og-default.jpg'),
        'url' => url()->current(),
        'type' => 'website',
        'site_name' => 'Liquid Lights',
        'locale' => 'en_US',
        'twitter_card' => 'summary_large_image',
        'twitter_site' => '@liquidlights',
    ];
    
    $meta = array_merge($defaultMeta, $meta);
    $keywords = is_array($meta['keywords']) ? implode(', ', $meta['keywords']) : $meta['keywords'];
@endphp

<!-- Basic Meta Tags -->
<title>{{ $meta['title'] }}</title>
<meta name="description" content="{{ $meta['description'] }}">
<meta name="keywords" content="{{ $keywords }}">
<meta name="author" content="Liquid Lights">
<meta name="robots" content="index, follow">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta charset="UTF-8">

<!-- Canonical URL -->
<link rel="canonical" href="{{ $meta['url'] }}">

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="{{ $meta['title'] }}">
<meta property="og:description" content="{{ $meta['description'] }}">
<meta property="og:image" content="{{ $meta['image'] }}">
<meta property="og:url" content="{{ $meta['url'] }}">
<meta property="og:type" content="{{ $meta['type'] }}">
<meta property="og:site_name" content="{{ $meta['site_name'] }}">
<meta property="og:locale" content="{{ $meta['locale'] }}">

@if(isset($meta['article_published_time']))
    <meta property="article:published_time" content="{{ $meta['article_published_time'] }}">
@endif

@if(isset($meta['article_modified_time']))
    <meta property="article:modified_time" content="{{ $meta['article_modified_time'] }}">
@endif

@if(isset($meta['article_author']))
    <meta property="article:author" content="{{ $meta['article_author'] }}">
@endif

@if(isset($meta['article_section']))
    <meta property="article:section" content="{{ $meta['article_section'] }}">
@endif

@if(isset($meta['article_tag']) && is_array($meta['article_tag']))
    @foreach($meta['article_tag'] as $tag)
        <meta property="article:tag" content="{{ $tag }}">
    @endforeach
@endif

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="{{ $meta['twitter_card'] }}">
<meta name="twitter:site" content="{{ $meta['twitter_site'] }}">
<meta name="twitter:title" content="{{ $meta['title'] }}">
<meta name="twitter:description" content="{{ $meta['description'] }}">
<meta name="twitter:image" content="{{ $meta['image'] }}">
<meta name="twitter:url" content="{{ $meta['url'] }}">

<!-- Additional SEO Meta Tags -->
<meta name="theme-color" content="#0a0a0a">
<meta name="msapplication-TileColor" content="#0a0a0a">
<meta name="application-name" content="Liquid Lights">

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
<link rel="manifest" href="/site.webmanifest">

<!-- Structured Data (JSON-LD) -->
@if($structuredData)
    <script type="application/ld+json">
        {!! json_encode($structuredData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
    </script>
@endif

<!-- Additional Structured Data for Organization -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Liquid Lights",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('images/logo.png') }}",
    "description": "Premium nightlife events and ticket booking platform",
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "Mumbai",
        "addressRegion": "Maharashtra",
        "addressCountry": "IN"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+91-98765-43210",
        "contactType": "customer service",
        "email": "<EMAIL>"
    },
    "sameAs": [
        "https://www.facebook.com/liquidlights",
        "https://www.instagram.com/liquidlights",
        "https://www.twitter.com/liquidlights"
    ]
}
</script>

<!-- Website Structured Data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Liquid Lights",
    "url": "{{ url('/') }}",
    "description": "Premium nightlife events and ticket booking platform",
    "potentialAction": {
        "@type": "SearchAction",
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": "{{ route('public.events') }}?search={search_term_string}"
        },
        "query-input": "required name=search_term_string"
    }
}
</script>
