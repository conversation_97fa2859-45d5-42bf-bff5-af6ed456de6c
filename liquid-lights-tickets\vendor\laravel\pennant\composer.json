{"name": "laravel/pennant", "description": "A simple, lightweight library for managing feature flags.", "keywords": ["laravel", "pennant", "feature", "flags"], "homepage": "https://github.com/laravel/pennant", "license": "MIT", "support": {"issues": "https://github.com/laravel/pennant/issues", "source": "https://github.com/laravel/pennant"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "illuminate/console": "^10.0|^11.0|^12.0", "illuminate/container": "^10.0|^11.0|^12.0", "illuminate/contracts": "^10.0|^11.0|^12.0", "illuminate/database": "^10.0|^11.0|^12.0", "illuminate/queue": "^10.0|^11.0|^12.0", "illuminate/support": "^10.0|^11.0|^12.0", "symfony/console": "^6.0|^7.0", "symfony/finder": "^6.0|^7.0"}, "require-dev": {"laravel/octane": "^1.4|^2.0", "orchestra/testbench": "^8.0|^9.0|^10.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.0|^10.4|^11.5"}, "autoload": {"psr-4": {"Laravel\\Pennant\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Workbench\\App\\": "workbench/app/", "Workbench\\Database\\Factories\\": "workbench/database/factories/"}}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}, "laravel": {"providers": ["Laravel\\Pennant\\PennantServiceProvider"], "aliases": {"Feature": "Laravel\\Pennant\\Feature"}}}, "config": {"sort-packages": true}, "scripts": {"post-autoload-dump": "@prepare", "prepare": "@php vendor/bin/testbench package:discover --ansi"}, "minimum-stability": "dev", "prefer-stable": true}