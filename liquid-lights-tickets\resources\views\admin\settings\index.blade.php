@extends('admin.layout')

@section('title', 'Settings')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-5">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Settings
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    Manage your application settings and configuration
                </p>
            </div>
            <div class="flex space-x-3">
                <button type="button" onclick="exportSettings()" 
                        class="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </button>
                <label class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 cursor-pointer">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Import
                    <input type="file" class="hidden" accept=".json" onchange="importSettings(this)">
                </label>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
        @csrf
        
        @if($settingsGroups->count() > 0)
            <!-- Settings Groups Tabs -->
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    @foreach($settingsGroups as $groupName => $settings)
                        <button type="button" 
                                onclick="showGroup('{{ $groupName }}')"
                                class="group-tab whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium {{ $loop->first ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' }}"
                                data-group="{{ $groupName }}">
                            {{ ucfirst($groupName) }}
                        </button>
                    @endforeach
                </nav>
            </div>

            <!-- Settings Groups Content -->
            @foreach($settingsGroups as $groupName => $settings)
                <div id="group-{{ $groupName }}" class="settings-group {{ !$loop->first ? 'hidden' : '' }}">
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">
                                {{ ucfirst($groupName) }} Settings
                            </h3>
                            
                            <div class="space-y-6">
                                @foreach($settings as $setting)
                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <div class="sm:col-span-2">
                                            <label for="setting_{{ $setting->key }}" class="block text-sm font-medium text-gray-700">
                                                {{ $setting->label }}
                                                @if($setting->is_required)
                                                    <span class="text-red-500">*</span>
                                                @endif
                                            </label>
                                            @if($setting->description)
                                                <p class="mt-1 text-sm text-gray-500">{{ $setting->description }}</p>
                                            @endif
                                        </div>
                                        
                                        <div class="sm:col-span-4">
                                            @if($setting->type === 'boolean')
                                                <div class="flex items-center">
                                                    <input type="hidden" name="settings[{{ $setting->key }}]" value="0">
                                                    <input type="checkbox" 
                                                           id="setting_{{ $setting->key }}"
                                                           name="settings[{{ $setting->key }}]" 
                                                           value="1"
                                                           {{ $setting->value ? 'checked' : '' }}
                                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                    <label for="setting_{{ $setting->key }}" class="ml-2 block text-sm text-gray-900">
                                                        Enable
                                                    </label>
                                                </div>
                                            @elseif($setting->type === 'file')
                                                <div class="space-y-2">
                                                    @if($setting->value)
                                                        <div class="flex items-center space-x-2">
                                                            <img src="{{ Storage::url($setting->value) }}" alt="Current file" class="h-10 w-10 object-cover rounded">
                                                            <span class="text-sm text-gray-500">Current file</span>
                                                        </div>
                                                    @endif
                                                    <input type="file" 
                                                           id="setting_{{ $setting->key }}"
                                                           name="settings[{{ $setting->key }}]"
                                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                                </div>
                                            @elseif($setting->options)
                                                <select id="setting_{{ $setting->key }}"
                                                        name="settings[{{ $setting->key }}]"
                                                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    @foreach($setting->options as $value => $label)
                                                        <option value="{{ $value }}" {{ $setting->value == $value ? 'selected' : '' }}>
                                                            {{ $label }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            @elseif($setting->type === 'json' && $setting->key === 'meta_keywords')
                                                <textarea id="setting_{{ $setting->key }}"
                                                          name="settings[{{ $setting->key }}]"
                                                          rows="3"
                                                          placeholder="Enter keywords separated by commas"
                                                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">{{ is_array($setting->value) ? implode(', ', $setting->value) : $setting->value }}</textarea>
                                            @else
                                                <input type="{{ $setting->type === 'integer' ? 'number' : 'text' }}" 
                                                       id="setting_{{ $setting->key }}"
                                                       name="settings[{{ $setting->key }}]" 
                                                       value="{{ $setting->value }}"
                                                       {{ $setting->is_required ? 'required' : '' }}
                                                       class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach

            <!-- Save Button -->
            <div class="flex justify-end">
                <button type="submit" 
                        class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Settings
                </button>
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No settings configured</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by running the settings seeder.</p>
                <div class="mt-6">
                    <button type="button" onclick="runSeeder()" 
                            class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500">
                        Initialize Settings
                    </button>
                </div>
            </div>
        @endif
    </form>
</div>

<script>
function showGroup(groupName) {
    // Hide all groups
    document.querySelectorAll('.settings-group').forEach(group => {
        group.classList.add('hidden');
    });
    
    // Show selected group
    document.getElementById('group-' + groupName).classList.remove('hidden');
    
    // Update tab styles
    document.querySelectorAll('.group-tab').forEach(tab => {
        tab.classList.remove('border-blue-500', 'text-blue-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });
    
    document.querySelector('[data-group="' + groupName + '"]').classList.remove('border-transparent', 'text-gray-500');
    document.querySelector('[data-group="' + groupName + '"]').classList.add('border-blue-500', 'text-blue-600');
}

function exportSettings() {
    window.location.href = '{{ route("admin.settings.export") }}';
}

function importSettings(input) {
    if (input.files && input.files[0]) {
        const formData = new FormData();
        formData.append('settings_file', input.files[0]);
        formData.append('_token', '{{ csrf_token() }}');
        
        fetch('{{ route("admin.settings.import") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Import failed: ' + data.message);
            }
        })
        .catch(error => {
            alert('Import failed: ' + error.message);
        });
    }
}

function runSeeder() {
    if (confirm('This will initialize default settings. Continue?')) {
        fetch('{{ route("admin.settings.reset") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            location.reload();
        })
        .catch(error => {
            alert('Failed to initialize settings: ' + error.message);
        });
    }
}
</script>
@endsection
