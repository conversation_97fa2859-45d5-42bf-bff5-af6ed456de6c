@extends('admin.layout')

@section('title', 'Blog Posts')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-5">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Blog Posts
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    Manage your blog posts and articles
                </p>
            </div>
            <div>
                <a href="{{ route('admin.blog-posts.create') }}" 
                   class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    New Post
                </a>
            </div>
        </div>
    </div>

    <!-- Coming Soon Message -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Blog Management Coming Soon</h3>
                <p class="mt-1 text-sm text-gray-500">
                    The blog post management interface is currently under development. 
                    The backend functionality is ready, and the admin interface will be available soon.
                </p>
                <div class="mt-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">
                                    What's Available Now:
                                </h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Blog post database model and migrations</li>
                                        <li>Full CRUD API endpoints</li>
                                        <li>Search and filtering capabilities</li>
                                        <li>Categories and tags support</li>
                                        <li>SEO meta management</li>
                                        <li>Featured posts functionality</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Documentation -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Available API Endpoints</h3>
            
            <div class="space-y-4">
                <div class="bg-gray-50 rounded-md p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Blog Posts API</h4>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span>
                            <code>/admin/blog-posts</code>
                            <span>- List all blog posts</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span>
                            <code>/admin/blog-posts</code>
                            <span>- Create new blog post</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span>
                            <code>/admin/blog-posts/{id}</code>
                            <span>- Show specific blog post</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span>
                            <code>/admin/blog-posts/{id}</code>
                            <span>- Update blog post</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">DELETE</span>
                            <code>/admin/blog-posts/{id}</code>
                            <span>- Delete blog post</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span>
                            <code>/admin/blog-posts/{id}/publish</code>
                            <span>- Publish blog post</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span>
                            <code>/admin/blog-posts/{id}/unpublish</code>
                            <span>- Unpublish blog post</span>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-md p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Blog Post Features</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div>
                            <h5 class="font-medium text-gray-900">Content Management:</h5>
                            <ul class="list-disc list-inside space-y-1 mt-1">
                                <li>Rich text content support</li>
                                <li>Featured images and galleries</li>
                                <li>Excerpt generation</li>
                                <li>Draft/Published/Archived status</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium text-gray-900">Organization:</h5>
                            <ul class="list-disc list-inside space-y-1 mt-1">
                                <li>Categories and tags</li>
                                <li>Featured posts</li>
                                <li>Search functionality</li>
                                <li>View and like tracking</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium text-gray-900">SEO Features:</h5>
                            <ul class="list-disc list-inside space-y-1 mt-1">
                                <li>Meta titles and descriptions</li>
                                <li>Keywords management</li>
                                <li>URL slug customization</li>
                                <li>Reading time calculation</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium text-gray-900">User Features:</h5>
                            <ul class="list-disc list-inside space-y-1 mt-1">
                                <li>Author attribution</li>
                                <li>Comments support</li>
                                <li>Publishing scheduling</li>
                                <li>Social sharing ready</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Status -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Database Status</h3>
            
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-3">
                <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Blog Posts Table</dt>
                                    <dd class="text-lg font-medium text-gray-900">Ready</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">API Controllers</dt>
                                    <dd class="text-lg font-medium text-gray-900">Ready</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Admin Interface</dt>
                                    <dd class="text-lg font-medium text-gray-900">In Progress</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
