<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Liquid Lights Tickets',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'is_public' => true,
                'is_required' => true,
                'sort_order' => 1
            ],
            [
                'key' => 'site_description',
                'value' => 'Premium nightlife and entertainment ticketing platform',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Site Description',
                'description' => 'A brief description of your website',
                'is_public' => true,
                'sort_order' => 2
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Contact Email',
                'description' => 'Primary contact email address',
                'is_public' => true,
                'validation_rules' => 'email',
                'sort_order' => 3
            ],
            [
                'key' => 'contact_phone',
                'value' => '+91 98765 43210',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Contact Phone',
                'description' => 'Primary contact phone number',
                'is_public' => true,
                'sort_order' => 4
            ],
            [
                'key' => 'address',
                'value' => 'Mumbai, Maharashtra, India',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Business Address',
                'description' => 'Primary business address',
                'is_public' => true,
                'sort_order' => 5
            ],
            // Appearance Settings
            [
                'key' => 'logo',
                'value' => null,
                'type' => 'file',
                'group' => 'appearance',
                'label' => 'Site Logo',
                'description' => 'Upload your site logo',
                'is_public' => true,
                'sort_order' => 1
            ],
            [
                'key' => 'favicon',
                'value' => null,
                'type' => 'file',
                'group' => 'appearance',
                'label' => 'Favicon',
                'description' => 'Upload your site favicon',
                'is_public' => true,
                'sort_order' => 2
            ],
            [
                'key' => 'primary_color',
                'value' => '#3B82F6',
                'type' => 'string',
                'group' => 'appearance',
                'label' => 'Primary Color',
                'description' => 'Primary brand color',
                'is_public' => true,
                'sort_order' => 3
            ],
            [
                'key' => 'secondary_color',
                'value' => '#8B5CF6',
                'type' => 'string',
                'group' => 'appearance',
                'label' => 'Secondary Color',
                'description' => 'Secondary brand color',
                'is_public' => true,
                'sort_order' => 4
            ],
            [
                'key' => 'dark_mode_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'appearance',
                'label' => 'Enable Dark Mode',
                'description' => 'Enable dark mode theme',
                'is_public' => true,
                'sort_order' => 5
            ],
            // Payment Settings
            [
                'key' => 'razorpay_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'payment',
                'label' => 'Enable Razorpay',
                'description' => 'Enable Razorpay payment gateway',
                'sort_order' => 1
            ],
            [
                'key' => 'stripe_enabled',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'payment',
                'label' => 'Enable Stripe',
                'description' => 'Enable Stripe payment gateway',
                'sort_order' => 2
            ],
            [
                'key' => 'payment_currency',
                'value' => 'INR',
                'type' => 'string',
                'group' => 'payment',
                'label' => 'Payment Currency',
                'description' => 'Default payment currency',
                'sort_order' => 3
            ],
            [
                'key' => 'tax_rate',
                'value' => '18',
                'type' => 'integer',
                'group' => 'payment',
                'label' => 'Tax Rate (%)',
                'description' => 'Default tax rate percentage',
                'sort_order' => 4
            ],
            // Notification Settings
            [
                'key' => 'email_notifications',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'notification',
                'label' => 'Email Notifications',
                'description' => 'Enable email notifications',
                'sort_order' => 1
            ],
            [
                'key' => 'sms_notifications',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'notification',
                'label' => 'SMS Notifications',
                'description' => 'Enable SMS notifications',
                'sort_order' => 2
            ],
            [
                'key' => 'push_notifications',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'notification',
                'label' => 'Push Notifications',
                'description' => 'Enable push notifications',
                'sort_order' => 3
            ],
            // SEO Settings
            [
                'key' => 'meta_title',
                'value' => 'Liquid Lights - Premium Nightlife Tickets',
                'type' => 'string',
                'group' => 'seo',
                'label' => 'Meta Title',
                'description' => 'Default meta title for pages',
                'is_public' => true,
                'sort_order' => 1
            ],
            [
                'key' => 'meta_description',
                'value' => 'Book tickets for the hottest nightlife events, concerts, and entertainment experiences. Premium venues, top DJs, and unforgettable nights.',
                'type' => 'string',
                'group' => 'seo',
                'label' => 'Meta Description',
                'description' => 'Default meta description for pages',
                'is_public' => true,
                'sort_order' => 2
            ],
            [
                'key' => 'meta_keywords',
                'value' => '["nightlife", "tickets", "events", "concerts", "entertainment", "booking"]',
                'type' => 'json',
                'group' => 'seo',
                'label' => 'Meta Keywords',
                'description' => 'Default meta keywords for pages',
                'is_public' => true,
                'sort_order' => 3
            ],
            // Social Media Settings
            [
                'key' => 'facebook_url',
                'value' => 'https://facebook.com/liquidlights',
                'type' => 'string',
                'group' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Facebook page URL',
                'is_public' => true,
                'sort_order' => 1
            ],
            [
                'key' => 'instagram_url',
                'value' => 'https://instagram.com/liquidlights',
                'type' => 'string',
                'group' => 'social',
                'label' => 'Instagram URL',
                'description' => 'Instagram profile URL',
                'is_public' => true,
                'sort_order' => 2
            ],
            [
                'key' => 'twitter_url',
                'value' => 'https://twitter.com/liquidlights',
                'type' => 'string',
                'group' => 'social',
                'label' => 'Twitter URL',
                'description' => 'Twitter profile URL',
                'is_public' => true,
                'sort_order' => 3
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
