<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use App\Models\Event;
use App\Models\Page;
use App\Models\BlogPost;
use App\Features;

class CacheOptimizationService
{
    protected array $cacheKeys = [
        'events' => [
            'featured_events' => 'events:featured',
            'upcoming_events' => 'events:upcoming',
            'popular_events' => 'events:popular',
            'event_categories' => 'events:categories',
        ],
        'pages' => [
            'published_pages' => 'pages:published',
            'menu_pages' => 'pages:menu',
        ],
        'blog' => [
            'recent_posts' => 'blog:recent',
            'featured_posts' => 'blog:featured',
        ],
        'analytics' => [
            'dashboard_stats' => 'analytics:dashboard',
            'event_stats' => 'analytics:events',
        ],
        'system' => [
            'settings' => 'system:settings',
            'feature_flags' => 'system:features',
        ]
    ];

    protected int $defaultTtl = 3600; // 1 hour
    protected int $longTtl = 86400; // 24 hours
    protected int $shortTtl = 300; // 5 minutes

    /**
     * Cache featured events
     */
    public function cacheFeaturedEvents(): void
    {
        if (!Features::enabled('caching-enhanced')) {
            return;
        }

        $key = $this->cacheKeys['events']['featured_events'];
        
        Cache::remember($key, $this->defaultTtl, function () {
            return Event::where('status', 'published')
                ->where('is_featured', true)
                ->where('event_date', '>=', now())
                ->with(['ticketTypes', 'artists'])
                ->orderBy('event_date')
                ->limit(6)
                ->get();
        });
    }

    /**
     * Cache upcoming events
     */
    public function cacheUpcomingEvents(): void
    {
        if (!Features::enabled('caching-enhanced')) {
            return;
        }

        $key = $this->cacheKeys['events']['upcoming_events'];
        
        Cache::remember($key, $this->defaultTtl, function () {
            return Event::where('status', 'published')
                ->where('event_date', '>=', now())
                ->with(['ticketTypes', 'artists'])
                ->orderBy('event_date')
                ->limit(12)
                ->get();
        });
    }

    /**
     * Cache dashboard statistics
     */
    public function cacheDashboardStats(): array
    {
        if (!Features::enabled('caching-enhanced')) {
            return $this->generateDashboardStats();
        }

        $key = $this->cacheKeys['analytics']['dashboard_stats'];
        
        return Cache::remember($key, $this->shortTtl, function () {
            return $this->generateDashboardStats();
        });
    }

    /**
     * Generate dashboard statistics
     */
    protected function generateDashboardStats(): array
    {
        return [
            'total_revenue' => [
                'formatted' => '₹' . number_format(DB::table('bookings')->sum('total_amount')),
                'raw' => DB::table('bookings')->sum('total_amount'),
            ],
            'total_bookings' => [
                'formatted' => number_format(DB::table('bookings')->count()),
                'raw' => DB::table('bookings')->count(),
            ],
            'active_events' => [
                'formatted' => number_format(Event::where('status', 'published')->where('event_date', '>=', now())->count()),
                'raw' => Event::where('status', 'published')->where('event_date', '>=', now())->count(),
            ],
            'total_tickets' => [
                'formatted' => number_format(DB::table('bookings')->sum('quantity')),
                'raw' => DB::table('bookings')->sum('quantity'),
            ],
            'new_users' => [
                'formatted' => number_format(DB::table('users')->where('created_at', '>=', now()->subDays(30))->count()),
                'raw' => DB::table('users')->where('created_at', '>=', now()->subDays(30))->count(),
            ],
            'avg_ticket_price' => [
                'formatted' => '₹' . number_format(DB::table('ticket_types')->avg('price')),
                'raw' => DB::table('ticket_types')->avg('price'),
            ],
        ];
    }

    /**
     * Cache page content
     */
    public function cachePage(string $slug): ?Page
    {
        if (!Features::enabled('caching-enhanced')) {
            return Page::where('slug', $slug)->where('status', 'published')->first();
        }

        $key = "page:{$slug}";
        
        return Cache::remember($key, $this->longTtl, function () use ($slug) {
            return Page::where('slug', $slug)->where('status', 'published')->first();
        });
    }

    /**
     * Cache event details
     */
    public function cacheEvent(string $slug): ?Event
    {
        if (!Features::enabled('caching-enhanced')) {
            return Event::where('slug', $slug)
                ->where('status', 'published')
                ->with(['ticketTypes', 'artists', 'sponsors'])
                ->first();
        }

        $key = "event:{$slug}";
        
        return Cache::remember($key, $this->defaultTtl, function () use ($slug) {
            return Event::where('slug', $slug)
                ->where('status', 'published')
                ->with(['ticketTypes', 'artists', 'sponsors'])
                ->first();
        });
    }

    /**
     * Cache menu pages
     */
    public function cacheMenuPages(): void
    {
        if (!Features::enabled('caching-enhanced')) {
            return;
        }

        $key = $this->cacheKeys['pages']['menu_pages'];
        
        Cache::remember($key, $this->longTtl, function () {
            return Page::where('status', 'published')
                ->where('show_in_menu', true)
                ->orderBy('menu_order')
                ->select(['title', 'slug'])
                ->get();
        });
    }

    /**
     * Invalidate event-related caches
     */
    public function invalidateEventCaches(): void
    {
        $keys = [
            $this->cacheKeys['events']['featured_events'],
            $this->cacheKeys['events']['upcoming_events'],
            $this->cacheKeys['events']['popular_events'],
            $this->cacheKeys['analytics']['dashboard_stats'],
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }

        // Clear individual event caches
        $this->clearEventCaches();
    }

    /**
     * Invalidate page-related caches
     */
    public function invalidatePageCaches(): void
    {
        $keys = [
            $this->cacheKeys['pages']['published_pages'],
            $this->cacheKeys['pages']['menu_pages'],
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }

        // Clear individual page caches
        $this->clearPageCaches();
    }

    /**
     * Clear all event caches
     */
    protected function clearEventCaches(): void
    {
        if (config('cache.default') === 'redis') {
            Redis::del(Redis::keys('event:*'));
        } else {
            // For file/database cache, we need to track keys manually
            $events = Event::pluck('slug');
            foreach ($events as $slug) {
                Cache::forget("event:{$slug}");
            }
        }
    }

    /**
     * Clear all page caches
     */
    protected function clearPageCaches(): void
    {
        if (config('cache.default') === 'redis') {
            Redis::del(Redis::keys('page:*'));
        } else {
            $pages = Page::pluck('slug');
            foreach ($pages as $slug) {
                Cache::forget("page:{$slug}");
            }
        }
    }

    /**
     * Warm up critical caches
     */
    public function warmUpCaches(): void
    {
        if (!Features::enabled('caching-enhanced')) {
            return;
        }

        // Warm up featured events
        $this->cacheFeaturedEvents();

        // Warm up upcoming events
        $this->cacheUpcomingEvents();

        // Warm up menu pages
        $this->cacheMenuPages();

        // Warm up dashboard stats
        $this->cacheDashboardStats();
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        $stats = [
            'driver' => config('cache.default'),
            'keys_count' => 0,
            'memory_usage' => 0,
            'hit_rate' => 0,
        ];

        if (config('cache.default') === 'redis') {
            try {
                $info = Redis::info();
                $stats['keys_count'] = Redis::dbsize();
                $stats['memory_usage'] = $info['used_memory_human'] ?? 'N/A';
                $stats['hit_rate'] = isset($info['keyspace_hits'], $info['keyspace_misses']) 
                    ? round(($info['keyspace_hits'] / ($info['keyspace_hits'] + $info['keyspace_misses'])) * 100, 2)
                    : 0;
            } catch (\Exception $e) {
                // Redis not available
            }
        }

        return $stats;
    }

    /**
     * Clear all application caches
     */
    public function clearAllCaches(): void
    {
        Cache::flush();
        
        // Clear specific cache tags if using tagged cache
        if (method_exists(Cache::store(), 'tags')) {
            Cache::tags(['events', 'pages', 'blog', 'analytics'])->flush();
        }
    }

    /**
     * Preload critical data
     */
    public function preloadCriticalData(): array
    {
        $data = [];

        // Preload featured events
        $data['featured_events'] = Cache::remember(
            $this->cacheKeys['events']['featured_events'],
            $this->defaultTtl,
            fn() => Event::where('status', 'published')
                ->where('is_featured', true)
                ->where('event_date', '>=', now())
                ->with(['ticketTypes'])
                ->limit(6)
                ->get()
        );

        // Preload menu pages
        $data['menu_pages'] = Cache::remember(
            $this->cacheKeys['pages']['menu_pages'],
            $this->longTtl,
            fn() => Page::where('status', 'published')
                ->where('show_in_menu', true)
                ->orderBy('menu_order')
                ->select(['title', 'slug'])
                ->get()
        );

        return $data;
    }

    /**
     * Schedule cache refresh
     */
    public function scheduleCacheRefresh(): void
    {
        // This would typically be called from a scheduled job
        $this->invalidateEventCaches();
        $this->warmUpCaches();
    }
}
