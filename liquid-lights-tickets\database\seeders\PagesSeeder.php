<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Page;
use App\Models\User;

class PagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();

        if (!$admin) {
            $this->command->error('Admin user not found. Please run UserSeeder first.');
            return;
        }

        $pages = [
            [
                'title' => 'About Us',
                'slug' => 'about-us',
                'content' => '<div class="prose max-w-none">
                    <h2>Welcome to Liquid Lights</h2>
                    <p>Liquid Lights is Mumbai\'s premier nightlife and entertainment ticketing platform, connecting party-goers with the hottest events, concerts, and exclusive experiences across the city.</p>

                    <h3>Our Mission</h3>
                    <p>To revolutionize the nightlife experience by providing seamless, secure, and innovative ticketing solutions that bring people together for unforgettable moments.</p>

                    <h3>What We Offer</h3>
                    <ul>
                        <li>Premium event ticketing with instant confirmation</li>
                        <li>Exclusive access to VIP experiences</li>
                        <li>Secure payment processing</li>
                        <li>Mobile-first design for on-the-go booking</li>
                        <li>Real-time event updates and notifications</li>
                    </ul>

                    <h3>Our Story</h3>
                    <p>Founded in 2025, Liquid Lights emerged from a passion for nightlife and technology. We recognized the need for a modern, user-friendly platform that could handle the unique demands of Mumbai\'s vibrant entertainment scene.</p>
                </div>',
                'excerpt' => 'Liquid Lights is Mumbai\'s premier nightlife and entertainment ticketing platform, connecting party-goers with the hottest events and exclusive experiences.',
                'meta_title' => 'About Liquid Lights - Premium Nightlife Ticketing',
                'meta_description' => 'Learn about Liquid Lights, Mumbai\'s leading nightlife ticketing platform. Discover our mission, story, and commitment to unforgettable experiences.',
                'meta_keywords' => ['about', 'liquid lights', 'nightlife', 'ticketing', 'mumbai', 'entertainment'],
                'status' => 'published',
                'show_in_menu' => true,
                'menu_order' => 1,
                'template' => 'default',
                'published_at' => now(),
                'created_by' => $admin->id,
                'updated_by' => $admin->id,
            ],
            [
                'title' => 'Contact Us',
                'slug' => 'contact-us',
                'content' => '<div class="prose max-w-none">
                    <h2>Get in Touch</h2>
                    <p>Have questions about our events or need assistance with your booking? We\'re here to help!</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 not-prose">
                        <div>
                            <h3 class="text-xl font-semibold mb-4">Contact Information</h3>
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    <span><EMAIL></span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    <span>+91 98765 43210</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <span>Mumbai, Maharashtra, India</span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-xl font-semibold mb-4">Business Hours</h3>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span>Monday - Friday:</span>
                                    <span>10:00 AM - 8:00 PM</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Saturday:</span>
                                    <span>10:00 AM - 10:00 PM</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Sunday:</span>
                                    <span>12:00 PM - 6:00 PM</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3>Follow Us</h3>
                    <p>Stay connected with us on social media for the latest updates, exclusive offers, and behind-the-scenes content.</p>
                </div>',
                'excerpt' => 'Get in touch with Liquid Lights for event inquiries, booking assistance, or general questions. We\'re here to help make your nightlife experience unforgettable.',
                'meta_title' => 'Contact Liquid Lights - Get in Touch',
                'meta_description' => 'Contact Liquid Lights for event inquiries, booking assistance, or support. Find our contact information, business hours, and location details.',
                'meta_keywords' => ['contact', 'support', 'help', 'liquid lights', 'customer service'],
                'status' => 'published',
                'show_in_menu' => true,
                'menu_order' => 2,
                'template' => 'contact',
                'published_at' => now(),
                'created_by' => $admin->id,
                'updated_by' => $admin->id,
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => '<div class="prose max-w-none">
                    <h2>Privacy Policy</h2>
                    <p><em>Last updated: ' . now()->format('F d, Y') . '</em></p>

                    <h3>Information We Collect</h3>
                    <p>We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support.</p>

                    <h3>How We Use Your Information</h3>
                    <ul>
                        <li>To provide and maintain our services</li>
                        <li>To process transactions and send related information</li>
                        <li>To send you technical notices and support messages</li>
                        <li>To communicate with you about products, services, and events</li>
                    </ul>

                    <h3>Information Sharing</h3>
                    <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

                    <h3>Data Security</h3>
                    <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

                    <h3>Contact Us</h3>
                    <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>
                </div>',
                'excerpt' => 'Learn how Liquid Lights collects, uses, and protects your personal information. Our commitment to your privacy and data security.',
                'meta_title' => 'Privacy Policy - Liquid Lights',
                'meta_description' => 'Read Liquid Lights\' privacy policy to understand how we collect, use, and protect your personal information.',
                'meta_keywords' => ['privacy', 'policy', 'data protection', 'security'],
                'status' => 'published',
                'show_in_menu' => false,
                'menu_order' => 99,
                'template' => 'default',
                'published_at' => now(),
                'created_by' => $admin->id,
                'updated_by' => $admin->id,
            ],
            [
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => '<div class="prose max-w-none">
                    <h2>Terms of Service</h2>
                    <p><em>Last updated: ' . now()->format('F d, Y') . '</em></p>

                    <h3>Acceptance of Terms</h3>
                    <p>By accessing and using Liquid Lights, you accept and agree to be bound by the terms and provision of this agreement.</p>

                    <h3>Ticket Purchases</h3>
                    <ul>
                        <li>All ticket sales are final unless otherwise specified</li>
                        <li>Tickets are non-transferable except through our official transfer system</li>
                        <li>Event organizers reserve the right to refuse entry</li>
                        <li>Age restrictions apply to certain events</li>
                    </ul>

                    <h3>Refund Policy</h3>
                    <p>Refunds are only available in case of event cancellation or as specified by the event organizer. Processing fees are non-refundable.</p>

                    <h3>User Conduct</h3>
                    <p>Users must not engage in any activity that disrupts or interferes with our services or servers.</p>

                    <h3>Limitation of Liability</h3>
                    <p>Liquid Lights shall not be liable for any indirect, incidental, special, consequential, or punitive damages.</p>

                    <h3>Contact Information</h3>
                    <p>For questions about these Terms of Service, contact <NAME_EMAIL>.</p>
                </div>',
                'excerpt' => 'Read the terms and conditions for using Liquid Lights ticketing platform, including ticket purchase policies and user responsibilities.',
                'meta_title' => 'Terms of Service - Liquid Lights',
                'meta_description' => 'Review Liquid Lights\' terms of service, including ticket purchase policies, refund terms, and user responsibilities.',
                'meta_keywords' => ['terms', 'service', 'conditions', 'legal', 'policy'],
                'status' => 'published',
                'show_in_menu' => false,
                'menu_order' => 98,
                'template' => 'default',
                'published_at' => now(),
                'created_by' => $admin->id,
                'updated_by' => $admin->id,
            ],
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }

        $this->command->info('Pages seeded successfully!');
    }
}
