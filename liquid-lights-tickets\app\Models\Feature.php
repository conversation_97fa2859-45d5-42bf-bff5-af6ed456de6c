<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Laravel\Pennant\Contracts\FeatureScope;

class Feature extends Model implements FeatureScope
{
    protected $primaryKey = 'name';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'scope',
        'value',
    ];

    protected $casts = [
        'value' => 'array',
    ];

    /**
     * Get the scope identifier for the feature.
     */
    public function __toString(): string
    {
        return $this->scope;
    }
}
