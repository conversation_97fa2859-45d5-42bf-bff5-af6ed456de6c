<?php

namespace App\Demo;

class DemoEvent
{
    public $id;
    public $title;
    public $description;
    public $short_description;
    public $event_date;
    public $event_time;
    public $start_time;
    public $end_time;
    public $venue;
    public $venue_name;
    public $venue_address;
    public $location;
    public $banner_image;
    public $thumbnail_image;
    public $featured_image;
    public $gallery_images;
    public $is_featured;
    public $status;
    public $capacity;
    public $min_price;
    public $max_price;
    public $category;
    public $duration;
    public $age_restriction;
    public $dress_code;
    public $terms_conditions;
    public $refund_policy;
    public $contact_phone;
    public $contact_email;
    public $social_media;
    public $tags;
    public $created_at;
    public $updated_at;
    public $ticket_types;
    public $artists;
    public $sponsors;

    public function __construct($data)
    {
        foreach ($data as $key => $value) {
            $this->$key = $value;
        }
    }

    /**
     * Check if event has sponsors
     */
    public function hasSponsors()
    {
        return $this->sponsors && $this->sponsors->count() > 0;
    }

    /**
     * Get active sponsors (demo implementation)
     */
    public function activeSponsors()
    {
        return new DemoActiveSponsorsRelation($this->sponsors ?? collect([]));
    }

    /**
     * Get sponsors by tier (demo implementation)
     */
    public function getSponsorsByTier($tierSlug = null)
    {
        if (!$this->sponsors) {
            return collect([]);
        }

        $sponsors = $this->sponsors;
        
        if ($tierSlug) {
            $sponsors = $sponsors->filter(function($sponsor) use ($tierSlug) {
                return isset($sponsor->tier) && $sponsor->tier === $tierSlug;
            });
        }

        return $sponsors->groupBy('tier');
    }

    /**
     * Magic method to handle property access
     */
    public function __get($name)
    {
        return $this->$name ?? null;
    }

    /**
     * Magic method to check if property exists
     */
    public function __isset($name)
    {
        return property_exists($this, $name);
    }
}

class DemoSponsorCollection
{
    private $sponsors;

    public function __construct($sponsors)
    {
        $this->sponsors = $sponsors;
    }

    public function count()
    {
        return $this->sponsors->count();
    }

    public function limit($limit)
    {
        return new DemoSponsorCollection($this->sponsors->take($limit));
    }

    public function get()
    {
        return $this->sponsors;
    }

    public function __call($method, $args)
    {
        return $this->sponsors->$method(...$args);
    }
}

class DemoActiveSponsorsRelation
{
    private $sponsors;

    public function __construct($sponsors)
    {
        $this->sponsors = $sponsors;
    }

    public function limit($limit)
    {
        return $this->sponsors->take($limit);
    }

    public function get()
    {
        return $this->sponsors;
    }

    public function count()
    {
        return $this->sponsors->count();
    }

    public function __call($method, $args)
    {
        return $this->sponsors->$method(...$args);
    }
}
