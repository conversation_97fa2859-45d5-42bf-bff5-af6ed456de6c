@extends('public.layout')

@section('title', 'Liquid Lights - Premium Nightlife Tickets')
@section('description', 'Experience the ultimate nightlife with premium events, top DJs, and unforgettable moments. Book tickets for the hottest events in the city.')

@section('content')
<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Background -->
    <div class="absolute inset-0 z-0">
        <div class="absolute inset-0 bg-gradient-to-br from-black/80 via-purple-900/60 to-black/90 z-10"></div>
        <img src="https://images.unsplash.com/photo-1503036563073-2cb917c92818?auto=format&fit=crop&w=1920&h=1080"
             alt="Nightlife Experience"
             class="w-full h-full object-cover opacity-40">
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-4 h-4 bg-blue-400 rounded-full floating pulse-neon"></div>
    <div class="absolute top-40 right-20 w-6 h-6 bg-purple-400 rounded-full floating pulse-neon" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-40 left-20 w-3 h-3 bg-pink-400 rounded-full floating pulse-neon" style="animation-delay: 2s;"></div>

    <!-- Hero Content -->
    <div class="relative z-20 text-center text-white px-4 max-w-6xl mx-auto">
        <div class="mb-12">
            <!-- Main Heading -->
            <h1 class="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight font-display">
                <span class="block neon-text">LIQUID</span>
                <span class="block neon-text-pink text-6xl md:text-8xl lg:text-9xl">LIGHTS</span>
            </h1>

            <!-- Subtitle -->
            <div class="glass-card p-8 max-w-4xl mx-auto mb-12">
                <p class="text-xl md:text-2xl leading-relaxed text-gray-200">
                    Experience the ultimate nightlife where
                    <span class="neon-text">music</span>,
                    <span class="neon-text-pink">lights</span>, and
                    <span class="neon-text-purple">luxury</span>
                    create unforgettable memories.
                </p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <a href="{{ route('public.events') }}" class="btn-primary text-lg px-8 py-4">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Explore Events
            </a>
            <a href="#featured-events" class="btn-secondary text-lg px-8 py-4">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
                See What's Hot
            </a>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold neon-text mb-2">50K+</div>
                <div class="text-sm text-gray-400">Happy Customers</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold neon-text-purple mb-2">200+</div>
                <div class="text-sm text-gray-400">Events Hosted</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold neon-text-pink mb-2">15+</div>
                <div class="text-sm text-gray-400">Premium Venues</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold neon-text mb-2">24/7</div>
                <div class="text-sm text-gray-400">Support</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Events Section -->
<section id="featured-events" class="py-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold font-display mb-6">
                <span class="neon-text">Featured</span> 
                <span class="text-white">Events</span>
            </h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Discover the hottest events happening right now. From exclusive parties to world-class concerts, 
                we bring you the best of nightlife entertainment.
            </p>
        </div>

        <!-- Events Grid -->
        @if(isset($events) && $events->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($events->take(6) as $event)
                    <div class="event-card group">
                        <!-- Event Image -->
                        <div class="relative overflow-hidden h-64">
                            @if($event->featured_image)
                                <img src="{{ Storage::url($event->featured_image) }}" 
                                     alt="{{ $event->title }}"
                                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            @else
                                <div class="w-full h-full bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Event Date Badge -->
                            <div class="absolute top-4 left-4 glass-card px-3 py-1">
                                <div class="text-sm font-semibold text-white">
                                    {{ \Carbon\Carbon::parse($event->event_date)->format('M d') }}
                                </div>
                            </div>

                            <!-- Price Badge -->
                            <div class="absolute top-4 right-4 bg-gradient-to-r from-green-500 to-blue-500 px-3 py-1 rounded-full">
                                <div class="text-sm font-bold text-white">
                                    @if($event->ticketTypes && $event->ticketTypes->count() > 0)
                                        ₹{{ number_format($event->ticketTypes->min('price')) }}
                                        @if($event->ticketTypes->min('price') != $event->ticketTypes->max('price'))
                                            +
                                        @endif
                                    @else
                                        TBA
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Event Details -->
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors">
                                {{ $event->title }}
                            </h3>
                            
                            <p class="text-gray-400 text-sm mb-4 line-clamp-2">
                                {{ Str::limit($event->description, 100) }}
                            </p>

                            <!-- Event Meta -->
                            <div class="space-y-2 mb-6">
                                <div class="flex items-center text-sm text-gray-400">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ \Carbon\Carbon::parse($event->event_date)->format('M d, Y') }} at {{ $event->event_time }}
                                </div>
                                <div class="flex items-center text-sm text-gray-400">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ $event->venue_name }}
                                </div>
                            </div>

                            <!-- Action Button -->
                            <a href="{{ route('public.events.show', $event->slug) }}" 
                               class="btn-primary w-full justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                                </svg>
                                Book Now
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- View All Events Button -->
            <div class="text-center mt-12">
                <a href="{{ route('public.events') }}" class="btn-secondary text-lg px-8 py-4">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    View All Events
                </a>
            </div>
        @else
            <!-- No Events State -->
            <div class="text-center py-16">
                <div class="glass-card p-12 max-w-md mx-auto">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-white mb-2">No Events Available</h3>
                    <p class="text-gray-400 mb-6">Check back soon for exciting new events!</p>
                    <a href="{{ route('public.events') }}" class="btn-primary">
                        Browse All Events
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-20 bg-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold font-display mb-6">
                <span class="text-white">Why Choose</span> 
                <span class="neon-text-purple">Liquid Lights</span>
            </h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                We're not just a ticketing platform - we're your gateway to extraordinary experiences.
            </p>
        </div>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="glass-card p-8 text-center group hover:border-blue-500 transition-all duration-300">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-4">Instant Booking</h3>
                <p class="text-gray-400">
                    Book your tickets instantly with our lightning-fast booking system. No waiting, no hassle.
                </p>
            </div>

            <!-- Feature 2 -->
            <div class="glass-card p-8 text-center group hover:border-purple-500 transition-all duration-300">
                <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-4">Secure Payments</h3>
                <p class="text-gray-400">
                    Your transactions are protected with bank-level security. Pay with confidence.
                </p>
            </div>

            <!-- Feature 3 -->
            <div class="glass-card p-8 text-center group hover:border-pink-500 transition-all duration-300">
                <div class="w-16 h-16 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-4">Premium Experience</h3>
                <p class="text-gray-400">
                    Access exclusive events, VIP experiences, and premium venues across the city.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-900 via-purple-900 to-pink-900">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-4xl md:text-5xl font-bold font-display text-white mb-6">
            Ready to <span class="neon-text">Experience</span> the Night?
        </h2>
        <p class="text-xl text-gray-200 mb-8">
            Join thousands of party-goers who trust Liquid Lights for their nightlife adventures.
        </p>
        <div class="flex flex-col sm:flex-row gap-6 justify-center">
            <a href="{{ route('public.events') }}" class="btn-primary text-lg px-8 py-4">
                Browse Events
            </a>
            @guest
                <a href="{{ route('register') }}" class="btn-secondary text-lg px-8 py-4">
                    Create Account
                </a>
            @endguest
        </div>
    </div>
</section>
@endsection
