@extends('public.layout')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20 lg:py-32 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute top-10 left-10 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div class="absolute top-40 right-20 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute bottom-20 left-1/3 w-72 h-72 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse" style="animation-delay: 4s;"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center fade-in">
            <!-- Main Heading -->
            <h1 class="heading-xl text-gray-900 mb-6">
                Discover Amazing
                <span class="text-gradient block">Events Near You</span>
            </h1>

            <!-- Subtitle -->
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
                From intimate concerts to grand festivals, find and book tickets for the most exciting events in your city. 
                Create memories that last a lifetime.
            </p>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                <a href="{{ route('public.events') }}" class="btn-primary text-lg px-8 py-4">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Explore Events
                </a>
                <a href="#featured-events" class="btn-secondary text-lg px-8 py-4">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                    Learn More
                </a>
            </div>

            <!-- Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div class="text-center slide-up">
                    <div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">50K+</div>
                    <div class="text-sm text-gray-600">Happy Customers</div>
                </div>
                <div class="text-center slide-up" style="animation-delay: 0.1s;">
                    <div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">200+</div>
                    <div class="text-sm text-gray-600">Events Hosted</div>
                </div>
                <div class="text-center slide-up" style="animation-delay: 0.2s;">
                    <div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">15+</div>
                    <div class="text-sm text-gray-600">Cities</div>
                </div>
                <div class="text-center slide-up" style="animation-delay: 0.3s;">
                    <div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">24/7</div>
                    <div class="text-sm text-gray-600">Support</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Events Section -->
<section id="featured-events" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16 fade-in">
            <h2 class="heading-lg text-gray-900 mb-6">
                Featured <span class="text-gradient">Events</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Discover the most popular and trending events happening right now. 
                Don't miss out on these amazing experiences.
            </p>
        </div>

        <!-- Events Grid -->
        @if(isset($featuredEvents) && $featuredEvents->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($featuredEvents->take(6) as $index => $event)
                    <div class="modern-card overflow-hidden slide-up" style="animation-delay: {{ $index * 0.1 }}s;">
                        <!-- Event Image -->
                        <div class="relative h-48 overflow-hidden">
                            @if($event->featured_image)
                                <img src="{{ Storage::url($event->featured_image) }}" 
                                     alt="{{ $event->title }}"
                                     class="w-full h-full object-cover transition-transform duration-500 hover:scale-110">
                            @else
                                <div class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Event Date Badge -->
                            <div class="absolute top-4 left-4 badge badge-primary">
                                {{ \Carbon\Carbon::parse($event->event_date)->format('M d') }}
                            </div>

                            <!-- Price Badge -->
                            @if($event->ticketTypes && $event->ticketTypes->count() > 0)
                                <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                                    <div class="text-sm font-bold text-gray-900">
                                        ₹{{ number_format($event->ticketTypes->min('price')) }}
                                        @if($event->ticketTypes->min('price') != $event->ticketTypes->max('price'))
                                            +
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Event Details -->
                        <div class="p-6">
                            <h3 class="heading-md text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                                {{ $event->title }}
                            </h3>
                            
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                {{ Str::limit(strip_tags($event->description), 100) }}
                            </p>

                            <!-- Event Meta -->
                            <div class="space-y-2 mb-6">
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ \Carbon\Carbon::parse($event->event_date)->format('M d, Y') }} at {{ $event->event_time }}
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ $event->venue_name }}
                                </div>
                            </div>

                            <!-- Action Button -->
                            <a href="{{ route('public.events.show', $event->slug) }}" 
                               class="btn-primary w-full justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                                </svg>
                                Book Now
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- View All Events Button -->
            <div class="text-center mt-12 fade-in">
                <a href="{{ route('public.events') }}" class="btn-secondary text-lg px-8 py-4">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    View All Events
                </a>
            </div>
        @else
            <!-- No Events State -->
            <div class="text-center py-16 fade-in">
                <div class="modern-card p-12 max-w-md mx-auto">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Events Available</h3>
                    <p class="text-gray-600 mb-6">Check back soon for exciting new events!</p>
                    <a href="{{ route('public.events') }}" class="btn-primary">
                        Browse Events
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16 fade-in">
            <h2 class="heading-lg text-gray-900 mb-6">
                Why Choose <span class="text-gradient">Liquid Lights</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We're not just a ticketing platform - we're your gateway to extraordinary experiences.
            </p>
        </div>

        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="modern-card p-8 text-center slide-up">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Instant Booking</h3>
                <p class="text-gray-600">
                    Book your tickets instantly with our lightning-fast booking system. No waiting, no hassle.
                </p>
            </div>

            <!-- Feature 2 -->
            <div class="modern-card p-8 text-center slide-up" style="animation-delay: 0.1s;">
                <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Secure Payments</h3>
                <p class="text-gray-600">
                    Your transactions are protected with bank-level security. Pay with confidence.
                </p>
            </div>

            <!-- Feature 3 -->
            <div class="modern-card p-8 text-center slide-up" style="animation-delay: 0.2s;">
                <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Premium Experience</h3>
                <p class="text-gray-600">
                    Access exclusive events, VIP experiences, and premium venues across the city.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 fade-in">
        <h2 class="heading-lg text-white mb-6">
            Ready to Experience Something Amazing?
        </h2>
        <p class="text-xl text-blue-100 mb-8">
            Join thousands of event-goers who trust Liquid Lights for their entertainment needs.
        </p>
        <div class="flex flex-col sm:flex-row gap-6 justify-center">
            <a href="{{ route('public.events') }}" class="bg-white text-gray-900 hover:bg-gray-100 px-8 py-4 rounded-xl font-semibold transition-all hover:transform hover:scale-105">
                Browse Events
            </a>
            @guest
                <a href="{{ route('register') }}" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-xl font-semibold transition-all">
                    Create Account
                </a>
            @endguest
        </div>
    </div>
</section>
@endsection
