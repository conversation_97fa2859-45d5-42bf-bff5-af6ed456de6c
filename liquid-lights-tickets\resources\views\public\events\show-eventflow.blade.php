@extends('public.layout')

@section('content')
<!-- Event Hero Section -->
<section class="relative bg-gray-900 overflow-hidden">
    <!-- Background Image -->
    @if($event->featured_image)
        <div class="absolute inset-0">
            <img src="{{ Storage::url($event->featured_image) }}" 
                 alt="{{ $event->title }}"
                 class="w-full h-full object-cover opacity-60">
            <div class="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent"></div>
        </div>
    @else
        <div class="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 opacity-80"></div>
    @endif

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Event Info -->
            <div class="text-white fade-in">
                <!-- Event Category -->
                @if($event->category)
                    <div class="inline-flex items-center px-3 py-1 rounded-full bg-white/20 backdrop-blur-sm text-sm font-medium mb-4">
                        {{ $event->category }}
                    </div>
                @endif

                <!-- Event Title -->
                <h1 class="heading-xl text-white mb-6">
                    {{ $event->title }}
                </h1>

                <!-- Event Meta -->
                <div class="space-y-4 mb-8">
                    <div class="flex items-center text-lg">
                        <svg class="w-6 h-6 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        {{ \Carbon\Carbon::parse($event->event_date)->format('l, F j, Y') }}
                    </div>
                    <div class="flex items-center text-lg">
                        <svg class="w-6 h-6 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ $event->event_time }}
                        @if($event->end_time)
                            - {{ $event->end_time }}
                        @endif
                    </div>
                    <div class="flex items-center text-lg">
                        <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        {{ $event->venue_name }}
                    </div>
                </div>

                <!-- Price Range -->
                @if($event->ticketTypes && $event->ticketTypes->count() > 0)
                    <div class="mb-8">
                        <div class="text-sm text-gray-300 mb-2">Starting from</div>
                        <div class="text-4xl font-bold text-white">
                            ₹{{ number_format($event->ticketTypes->min('price')) }}
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#tickets" class="btn-primary text-lg px-8 py-4">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                        </svg>
                        Book Tickets
                    </a>
                    <button onclick="shareEvent()" class="btn-secondary bg-white/10 border-white/20 text-white hover:bg-white/20 text-lg px-8 py-4">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                        Share
                    </button>
                </div>
            </div>

            <!-- Event Stats -->
            <div class="slide-up">
                <div class="modern-card bg-white/10 backdrop-blur-sm border-white/20 p-8">
                    <h3 class="text-xl font-bold text-white mb-6">Event Stats</h3>
                    <div class="grid grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white mb-2">{{ $event->capacity ?? '500' }}</div>
                            <div class="text-sm text-gray-300">Capacity</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white mb-2">{{ $event->ticketTypes ? $event->ticketTypes->count() : '3' }}</div>
                            <div class="text-sm text-gray-300">Ticket Types</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white mb-2">4.8</div>
                            <div class="text-sm text-gray-300">Rating</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white mb-2">{{ rand(50, 200) }}</div>
                            <div class="text-sm text-gray-300">Interested</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Event Details Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- About Event -->
                <div class="modern-card p-8 mb-8 fade-in">
                    <h2 class="heading-md text-gray-900 mb-6">About This Event</h2>
                    <div class="prose prose-lg max-w-none text-gray-600">
                        {!! $event->description !!}
                    </div>
                </div>

                <!-- Artists/Performers -->
                @if($event->artists && $event->artists->count() > 0)
                    <div class="modern-card p-8 mb-8 slide-up">
                        <h2 class="heading-md text-gray-900 mb-6">Featured Artists</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($event->artists as $artist)
                                <div class="flex items-center p-4 bg-gray-50 rounded-xl">
                                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                                        <span class="text-white font-bold text-lg">{{ substr($artist->name, 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-gray-900">{{ $artist->name }}</h3>
                                        <p class="text-sm text-gray-600">{{ $artist->genre ?? 'Artist' }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Venue Information -->
                <div class="modern-card p-8 slide-up">
                    <h2 class="heading-md text-gray-900 mb-6">Venue Information</h2>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-purple-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <div>
                                <h3 class="font-semibold text-gray-900">{{ $event->venue_name }}</h3>
                                <p class="text-gray-600">{{ $event->venue_address }}</p>
                            </div>
                        </div>
                        
                        <!-- Map placeholder -->
                        <div class="h-64 bg-gray-100 rounded-xl flex items-center justify-center">
                            <div class="text-center">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                </svg>
                                <p class="text-gray-500">Interactive map coming soon</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Ticket Booking -->
                <div id="tickets" class="modern-card p-6 mb-8 sticky top-24 fade-in">
                    <h3 class="text-xl font-bold text-gray-900 mb-6">Select Tickets</h3>
                    
                    @if($event->ticketTypes && $event->ticketTypes->count() > 0)
                        <div class="space-y-4">
                            @foreach($event->ticketTypes as $ticketType)
                                <div class="border border-gray-200 rounded-xl p-4 hover:border-blue-300 transition-colors">
                                    <div class="flex justify-between items-start mb-3">
                                        <div>
                                            <h4 class="font-semibold text-gray-900">{{ $ticketType->name }}</h4>
                                            <p class="text-sm text-gray-600">{{ $ticketType->description }}</p>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-lg font-bold text-gray-900">₹{{ number_format($ticketType->price) }}</div>
                                            <div class="text-xs text-gray-500">{{ $ticketType->quantity_available }} left</div>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <button class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                                </svg>
                                            </button>
                                            <span class="w-8 text-center font-medium">0</span>
                                            <button class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                </svg>
                                            </button>
                                        </div>
                                        
                                        @if($ticketType->quantity_available > 0)
                                            <span class="badge badge-success">Available</span>
                                        @else
                                            <span class="badge badge-warning">Sold Out</span>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Booking Summary -->
                        <div class="mt-6 p-4 bg-gray-50 rounded-xl">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600">Total</span>
                                <span class="text-xl font-bold text-gray-900">₹0</span>
                            </div>
                            <button class="btn-primary w-full justify-center mt-4">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0L4 5H2m5 8h10m0 0l1.5 6M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
                                </svg>
                                Proceed to Checkout
                            </button>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Tickets Coming Soon</h3>
                            <p class="text-gray-600 text-sm">Ticket sales will begin shortly. Check back soon!</p>
                        </div>
                    @endif
                </div>

                <!-- Event Organizer -->
                <div class="modern-card p-6 slide-up">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Event Organizer</h3>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">LL</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Liquid Lights</h4>
                            <p class="text-sm text-gray-600">Premium Event Organizer</p>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                            Contact Organizer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    function shareEvent() {
        if (navigator.share) {
            navigator.share({
                title: '{{ $event->title }}',
                text: 'Check out this amazing event!',
                url: window.location.href
            });
        } else {
            // Fallback to copying URL
            navigator.clipboard.writeText(window.location.href);
            alert('Event URL copied to clipboard!');
        }
    }
</script>
@endsection
