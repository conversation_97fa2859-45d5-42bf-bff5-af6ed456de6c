<?php

namespace App\Services;

use App\Models\Event;
use App\Models\Page;
use App\Models\BlogPost;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SEOService
{
    /**
     * Generate meta tags for a given page/model
     */
    public function generateMetaTags($model, array $overrides = []): array
    {
        $defaults = $this->getDefaultMetaTags();
        
        if ($model instanceof Event) {
            $meta = $this->getEventMetaTags($model);
        } elseif ($model instanceof Page) {
            $meta = $this->getPageMetaTags($model);
        } elseif ($model instanceof BlogPost) {
            $meta = $this->getBlogPostMetaTags($model);
        } else {
            $meta = $defaults;
        }

        return array_merge($defaults, $meta, $overrides);
    }

    /**
     * Get default meta tags
     */
    private function getDefaultMetaTags(): array
    {
        return [
            'title' => 'Liquid Lights - Premium Nightlife Tickets',
            'description' => 'Experience the ultimate nightlife with premium events, top DJs, and unforgettable moments. Book tickets for the hottest events in the city.',
            'keywords' => ['nightlife', 'events', 'tickets', 'concerts', 'parties', 'entertainment'],
            'image' => asset('images/og-default.jpg'),
            'url' => url()->current(),
            'type' => 'website',
            'site_name' => 'Liquid Lights',
            'locale' => 'en_US',
            'twitter_card' => 'summary_large_image',
            'twitter_site' => '@liquidlights',
        ];
    }

    /**
     * Get meta tags for events
     */
    private function getEventMetaTags(Event $event): array
    {
        $image = $event->featured_image 
            ? Storage::url($event->featured_image)
            : asset('images/event-placeholder.jpg');

        $keywords = array_merge(
            ['event', 'tickets', $event->title],
            $event->artists ? $event->artists->pluck('name')->toArray() : [],
            [$event->venue_name]
        );

        return [
            'title' => $event->meta_title ?: "{$event->title} - Liquid Lights",
            'description' => $event->meta_description ?: Str::limit(strip_tags($event->description), 160),
            'keywords' => $event->meta_keywords ?: $keywords,
            'image' => $image,
            'url' => route('public.events.show', $event->slug),
            'type' => 'article',
            'article_published_time' => $event->created_at->toISOString(),
            'article_modified_time' => $event->updated_at->toISOString(),
            'article_author' => 'Liquid Lights',
            'article_section' => 'Events',
            'article_tag' => $keywords,
        ];
    }

    /**
     * Get meta tags for pages
     */
    private function getPageMetaTags(Page $page): array
    {
        $image = $page->featured_image 
            ? Storage::url($page->featured_image)
            : asset('images/page-placeholder.jpg');

        return [
            'title' => $page->meta_title ?: "{$page->title} - Liquid Lights",
            'description' => $page->meta_description ?: Str::limit(strip_tags($page->content), 160),
            'keywords' => $page->meta_keywords ?: [$page->title, 'liquid lights'],
            'image' => $image,
            'url' => url("/{$page->slug}"),
            'type' => 'article',
        ];
    }

    /**
     * Get meta tags for blog posts
     */
    private function getBlogPostMetaTags(BlogPost $post): array
    {
        $image = $post->featured_image 
            ? Storage::url($post->featured_image)
            : asset('images/blog-placeholder.jpg');

        $keywords = array_merge(
            ['blog', $post->title],
            $post->tags ? $post->tags->pluck('name')->toArray() : []
        );

        return [
            'title' => $post->meta_title ?: "{$post->title} - Liquid Lights Blog",
            'description' => $post->meta_description ?: Str::limit(strip_tags($post->content), 160),
            'keywords' => $post->meta_keywords ?: $keywords,
            'image' => $image,
            'url' => route('public.blog.show', $post->slug),
            'type' => 'article',
            'article_published_time' => $post->published_at?->toISOString(),
            'article_modified_time' => $post->updated_at->toISOString(),
            'article_author' => $post->author?->name ?: 'Liquid Lights',
            'article_section' => 'Blog',
            'article_tag' => $keywords,
        ];
    }

    /**
     * Generate structured data (JSON-LD) for events
     */
    public function generateEventStructuredData(Event $event): array
    {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'Event',
            'name' => $event->title,
            'description' => strip_tags($event->description),
            'startDate' => $event->event_date . 'T' . $event->event_time,
            'endDate' => $event->end_date ? $event->end_date . 'T' . ($event->end_time ?: '23:59') : null,
            'eventStatus' => 'https://schema.org/EventScheduled',
            'eventAttendanceMode' => 'https://schema.org/OfflineEventAttendanceMode',
            'location' => [
                '@type' => 'Place',
                'name' => $event->venue_name,
                'address' => [
                    '@type' => 'PostalAddress',
                    'streetAddress' => $event->venue_address,
                    'addressLocality' => $event->venue_city,
                    'addressCountry' => 'IN'
                ]
            ],
            'organizer' => [
                '@type' => 'Organization',
                'name' => 'Liquid Lights',
                'url' => url('/')
            ],
            'url' => route('public.events.show', $event->slug),
        ];

        // Add image if available
        if ($event->featured_image) {
            $structuredData['image'] = Storage::url($event->featured_image);
        }

        // Add offers (ticket types)
        if ($event->ticketTypes && $event->ticketTypes->count() > 0) {
            $offers = [];
            foreach ($event->ticketTypes as $ticketType) {
                $offers[] = [
                    '@type' => 'Offer',
                    'name' => $ticketType->name,
                    'price' => $ticketType->price,
                    'priceCurrency' => 'INR',
                    'availability' => $ticketType->quantity_available > 0 
                        ? 'https://schema.org/InStock' 
                        : 'https://schema.org/OutOfStock',
                    'url' => route('public.events.show', $event->slug),
                    'validFrom' => $event->created_at->toISOString(),
                ];
            }
            $structuredData['offers'] = $offers;
        }

        // Add performers if available
        if ($event->artists && $event->artists->count() > 0) {
            $performers = [];
            foreach ($event->artists as $artist) {
                $performers[] = [
                    '@type' => 'Person',
                    'name' => $artist->name,
                ];
            }
            $structuredData['performer'] = $performers;
        }

        return $structuredData;
    }

    /**
     * Generate sitemap XML
     */
    public function generateSitemap(): string
    {
        $urls = [];

        // Add static pages
        $urls[] = [
            'loc' => url('/'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'daily',
            'priority' => '1.0'
        ];

        $urls[] = [
            'loc' => route('public.events'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'daily',
            'priority' => '0.9'
        ];

        // Add events
        Event::where('status', 'published')
            ->where('event_date', '>=', now())
            ->orderBy('updated_at', 'desc')
            ->chunk(100, function ($events) use (&$urls) {
                foreach ($events as $event) {
                    $urls[] = [
                        'loc' => route('public.events.show', $event->slug),
                        'lastmod' => $event->updated_at->toISOString(),
                        'changefreq' => 'weekly',
                        'priority' => '0.8'
                    ];
                }
            });

        // Add pages
        Page::where('status', 'published')
            ->orderBy('updated_at', 'desc')
            ->chunk(100, function ($pages) use (&$urls) {
                foreach ($pages as $page) {
                    $urls[] = [
                        'loc' => url("/{$page->slug}"),
                        'lastmod' => $page->updated_at->toISOString(),
                        'changefreq' => 'monthly',
                        'priority' => '0.6'
                    ];
                }
            });

        // Add blog posts
        BlogPost::where('status', 'published')
            ->whereNotNull('published_at')
            ->orderBy('updated_at', 'desc')
            ->chunk(100, function ($posts) use (&$urls) {
                foreach ($posts as $post) {
                    $urls[] = [
                        'loc' => route('public.blog.show', $post->slug),
                        'lastmod' => $post->updated_at->toISOString(),
                        'changefreq' => 'monthly',
                        'priority' => '0.7'
                    ];
                }
            });

        return $this->buildSitemapXML($urls);
    }

    /**
     * Build sitemap XML from URLs array
     */
    private function buildSitemapXML(array $urls): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>" . htmlspecialchars($url['loc']) . "</loc>\n";
            $xml .= "    <lastmod>" . $url['lastmod'] . "</lastmod>\n";
            $xml .= "    <changefreq>" . $url['changefreq'] . "</changefreq>\n";
            $xml .= "    <priority>" . $url['priority'] . "</priority>\n";
            $xml .= "  </url>\n";
        }

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Cache and return sitemap
     */
    public function getCachedSitemap(): string
    {
        return Cache::remember('sitemap', 3600, function () {
            return $this->generateSitemap();
        });
    }

    /**
     * Clear sitemap cache
     */
    public function clearSitemapCache(): void
    {
        Cache::forget('sitemap');
    }

    /**
     * Generate robots.txt content
     */
    public function generateRobotsTxt(): string
    {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Disallow: /admin/\n";
        $content .= "Disallow: /api/\n";
        $content .= "Disallow: /user/\n";
        $content .= "\n";
        $content .= "Sitemap: " . url('/sitemap.xml') . "\n";

        return $content;
    }
}
