@extends('admin.layout')

@section('title', 'Feature Flags')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="border-b border-gray-200 pb-5">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Feature Flags
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    Manage feature flags and A/B testing configurations
                </p>
            </div>
            <div class="flex space-x-3">
                <button onclick="resetAllFeatures()" 
                        class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset All
                </button>
                <button onclick="exportConfig()" 
                        class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </button>
            </div>
        </div>
    </div>

    <!-- Feature Categories -->
    @foreach($featureStatus as $category => $features)
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">{{ $category }}</h3>
                
                <div class="space-y-4">
                    @foreach($features as $featureName => $featureData)
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <h4 class="text-sm font-medium text-gray-900">
                                        {{ ucwords(str_replace('-', ' ', $featureName)) }}
                                    </h4>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $featureData['enabled'] ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $featureData['enabled'] ? 'Enabled' : 'Disabled' }}
                                    </span>
                                </div>
                                <p class="mt-1 text-sm text-gray-500">{{ $featureData['description'] }}</p>
                            </div>
                            
                            <div class="ml-4">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" 
                                           class="sr-only peer feature-toggle" 
                                           data-feature="{{ $featureName }}"
                                           {{ $featureData['enabled'] ? 'checked' : '' }}
                                           onchange="toggleFeature('{{ $featureName }}', this.checked)">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endforeach

    <!-- Bulk Actions -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Bulk Actions</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="enableAllFeatures()" 
                        class="inline-flex items-center justify-center rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Enable All
                </button>
                
                <button onclick="disableAllFeatures()" 
                        class="inline-flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Disable All
                </button>
                
                <button onclick="showImportModal()" 
                        class="inline-flex items-center justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500">
                    <svg class="-ml-0.5 mr-1.5 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                    </svg>
                    Import Config
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Statistics</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                @php
                    $totalFeatures = 0;
                    $enabledFeatures = 0;
                    foreach($featureStatus as $features) {
                        foreach($features as $feature) {
                            $totalFeatures++;
                            if($feature['enabled']) $enabledFeatures++;
                        }
                    }
                    $disabledFeatures = $totalFeatures - $enabledFeatures;
                    $enabledPercentage = $totalFeatures > 0 ? round(($enabledFeatures / $totalFeatures) * 100) : 0;
                @endphp
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-900">{{ $totalFeatures }}</div>
                    <div class="text-sm text-gray-500">Total Features</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ $enabledFeatures }}</div>
                    <div class="text-sm text-gray-500">Enabled</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600">{{ $disabledFeatures }}</div>
                    <div class="text-sm text-gray-500">Disabled</div>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ $enabledPercentage }}%</div>
                    <div class="text-sm text-gray-500">Enabled Rate</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Import Feature Configuration</h3>
            <textarea id="importConfig" 
                      rows="10" 
                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                      placeholder="Paste your feature configuration JSON here..."></textarea>
            <div class="flex justify-end space-x-3 mt-4">
                <button onclick="hideImportModal()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    Cancel
                </button>
                <button onclick="importConfig()" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Import
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle individual feature
function toggleFeature(feature, enabled) {
    fetch('/admin/feature-flags/toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            feature: feature,
            enabled: enabled
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
        } else {
            showNotification('Failed to toggle feature', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

// Enable all features
function enableAllFeatures() {
    const features = [];
    document.querySelectorAll('.feature-toggle').forEach(toggle => {
        features.push({
            feature: toggle.dataset.feature,
            enabled: true
        });
        toggle.checked = true;
    });
    
    bulkToggleFeatures(features);
}

// Disable all features
function disableAllFeatures() {
    const features = [];
    document.querySelectorAll('.feature-toggle').forEach(toggle => {
        features.push({
            feature: toggle.dataset.feature,
            enabled: false
        });
        toggle.checked = false;
    });
    
    bulkToggleFeatures(features);
}

// Bulk toggle features
function bulkToggleFeatures(features) {
    fetch('/admin/feature-flags/bulk-toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            features: features
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Failed to update features', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

// Reset all features
function resetAllFeatures() {
    if (confirm('Are you sure you want to reset all features to their default values?')) {
        fetch('/admin/feature-flags/reset', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('Failed to reset features', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred', 'error');
        });
    }
}

// Export configuration
function exportConfig() {
    fetch('/admin/feature-flags/export')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const blob = new Blob([JSON.stringify(data.config, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'feature-flags-config.json';
            a.click();
            URL.revokeObjectURL(url);
            showNotification('Configuration exported successfully', 'success');
        } else {
            showNotification('Failed to export configuration', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

// Show import modal
function showImportModal() {
    document.getElementById('importModal').classList.remove('hidden');
}

// Hide import modal
function hideImportModal() {
    document.getElementById('importModal').classList.add('hidden');
    document.getElementById('importConfig').value = '';
}

// Import configuration
function importConfig() {
    const configText = document.getElementById('importConfig').value;
    
    try {
        const config = JSON.parse(configText);
        
        fetch('/admin/feature-flags/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                config: config
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                hideImportModal();
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('Failed to import configuration', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred', 'error');
        });
    } catch (error) {
        showNotification('Invalid JSON format', 'error');
    }
}

// Show notification
function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endsection
