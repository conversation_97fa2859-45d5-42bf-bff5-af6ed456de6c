<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\PerformanceMonitoringService;
use App\Services\CacheOptimizationService;
use App\Services\ImageOptimizationService;

class PerformanceController extends Controller
{
    protected PerformanceMonitoringService $performanceService;
    protected CacheOptimizationService $cacheService;
    protected ImageOptimizationService $imageService;

    public function __construct(
        PerformanceMonitoringService $performanceService,
        CacheOptimizationService $cacheService,
        ImageOptimizationService $imageService
    ) {
        $this->performanceService = $performanceService;
        $this->cacheService = $cacheService;
        $this->imageService = $imageService;
    }

    /**
     * Display performance dashboard
     */
    public function index()
    {
        $realTimeMetrics = $this->performanceService->getRealTimeMetrics();
        $performanceReport = $this->performanceService->getPerformanceReport(24);
        $recommendations = $this->performanceService->getPerformanceRecommendations();
        $cacheStats = $this->cacheService->getCacheStats();

        return view('admin.performance.index', compact(
            'realTimeMetrics',
            'performanceReport',
            'recommendations',
            'cacheStats'
        ));
    }

    /**
     * Get performance metrics as JSON
     */
    public function metrics(Request $request)
    {
        $hours = $request->input('hours', 24);
        
        return response()->json([
            'real_time' => $this->performanceService->getRealTimeMetrics(),
            'report' => $this->performanceService->getPerformanceReport($hours),
            'recommendations' => $this->performanceService->getPerformanceRecommendations(),
        ]);
    }

    /**
     * Clear all caches
     */
    public function clearCaches()
    {
        try {
            $this->cacheService->clearAllCaches();
            
            return response()->json([
                'success' => true,
                'message' => 'All caches cleared successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear caches: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Warm up caches
     */
    public function warmUpCaches()
    {
        try {
            $this->cacheService->warmUpCaches();
            
            return response()->json([
                'success' => true,
                'message' => 'Caches warmed up successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to warm up caches: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Optimize images
     */
    public function optimizeImages(Request $request)
    {
        $request->validate([
            'paths' => 'required|array',
            'quality' => 'integer|min:10|max:100',
        ]);

        try {
            $results = $this->imageService->batchOptimize(
                $request->input('paths'),
                $request->input('quality', 80)
            );
            
            return response()->json([
                'success' => true,
                'message' => 'Images optimized successfully',
                'results' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to optimize images: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clear old performance metrics
     */
    public function clearOldMetrics(Request $request)
    {
        $hoursToKeep = $request->input('hours', 168); // Default: 1 week
        
        try {
            $this->performanceService->clearOldMetrics($hoursToKeep);
            
            return response()->json([
                'success' => true,
                'message' => "Old performance metrics cleared (kept last {$hoursToKeep} hours)",
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear old metrics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get cache statistics
     */
    public function cacheStats()
    {
        return response()->json([
            'success' => true,
            'stats' => $this->cacheService->getCacheStats(),
        ]);
    }

    /**
     * Invalidate specific cache
     */
    public function invalidateCache(Request $request)
    {
        $request->validate([
            'type' => 'required|in:events,pages,blog,all',
        ]);

        try {
            switch ($request->input('type')) {
                case 'events':
                    $this->cacheService->invalidateEventCaches();
                    break;
                case 'pages':
                    $this->cacheService->invalidatePageCaches();
                    break;
                case 'all':
                    $this->cacheService->clearAllCaches();
                    break;
            }
            
            return response()->json([
                'success' => true,
                'message' => ucfirst($request->input('type')) . ' caches invalidated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to invalidate cache: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Run performance tests
     */
    public function runTests()
    {
        $tests = [];

        // Test database connection
        $this->performanceService->startTimer('database_test');
        try {
            \DB::connection()->getPdo();
            $dbMetrics = $this->performanceService->endTimer('database_test');
            $tests['database'] = [
                'status' => 'success',
                'duration' => $dbMetrics['duration'],
                'message' => 'Database connection successful',
            ];
        } catch (\Exception $e) {
            $tests['database'] = [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage(),
            ];
        }

        // Test cache
        $this->performanceService->startTimer('cache_test');
        try {
            \Cache::put('performance_test', 'test_value', 60);
            $value = \Cache::get('performance_test');
            \Cache::forget('performance_test');
            
            $cacheMetrics = $this->performanceService->endTimer('cache_test');
            $tests['cache'] = [
                'status' => $value === 'test_value' ? 'success' : 'error',
                'duration' => $cacheMetrics['duration'],
                'message' => $value === 'test_value' ? 'Cache working correctly' : 'Cache test failed',
            ];
        } catch (\Exception $e) {
            $tests['cache'] = [
                'status' => 'error',
                'message' => 'Cache test failed: ' . $e->getMessage(),
            ];
        }

        // Test file system
        $this->performanceService->startTimer('filesystem_test');
        try {
            \Storage::disk('public')->put('performance_test.txt', 'test content');
            $content = \Storage::disk('public')->get('performance_test.txt');
            \Storage::disk('public')->delete('performance_test.txt');
            
            $fsMetrics = $this->performanceService->endTimer('filesystem_test');
            $tests['filesystem'] = [
                'status' => $content === 'test content' ? 'success' : 'error',
                'duration' => $fsMetrics['duration'],
                'message' => $content === 'test content' ? 'File system working correctly' : 'File system test failed',
            ];
        } catch (\Exception $e) {
            $tests['filesystem'] = [
                'status' => 'error',
                'message' => 'File system test failed: ' . $e->getMessage(),
            ];
        }

        return response()->json([
            'success' => true,
            'tests' => $tests,
            'overall_status' => collect($tests)->every(fn($test) => $test['status'] === 'success') ? 'success' : 'warning',
        ]);
    }

    /**
     * Export performance report
     */
    public function exportReport(Request $request)
    {
        $hours = $request->input('hours', 168); // Default: 1 week
        $report = $this->performanceService->getPerformanceReport($hours);
        
        $filename = 'performance_report_' . now()->format('Y-m-d_H-i-s') . '.json';
        
        return response()->json($report)
            ->header('Content-Disposition', "attachment; filename={$filename}");
    }
}
