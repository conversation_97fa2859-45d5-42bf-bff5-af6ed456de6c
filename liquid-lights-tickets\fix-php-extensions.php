<?php

echo "🔧 PHP EXTENSIONS FIX SCRIPT 🔧\n";
echo "================================\n\n";

// Get PHP info
$phpVersion = phpversion();
$phpIniPath = php_ini_loaded_file();
$extensionDir = ini_get('extension_dir');

echo "📋 SYSTEM INFO:\n";
echo "===============\n";
echo "PHP Version: {$phpVersion}\n";
echo "PHP INI Path: {$phpIniPath}\n";
echo "Extension Dir: {$extensionDir}\n\n";

// Check current extensions
echo "📦 CURRENT EXTENSIONS:\n";
echo "======================\n";

$requiredExtensions = [
    'pdo' => extension_loaded('pdo'),
    'pdo_mysql' => extension_loaded('pdo_mysql'),
    'mysqli' => extension_loaded('mysqli'),
    'sqlite3' => extension_loaded('sqlite3'),
    'pdo_sqlite' => extension_loaded('pdo_sqlite'),
    'fileinfo' => extension_loaded('fileinfo'),
    'mbstring' => extension_loaded('mbstring'),
    'openssl' => extension_loaded('openssl'),
    'curl' => extension_loaded('curl'),
    'xml' => extension_loaded('xml'),
    'json' => extension_loaded('json'),
];

foreach ($requiredExtensions as $ext => $loaded) {
    $status = $loaded ? '✅ LOADED' : '❌ NOT LOADED';
    echo "{$ext}: {$status}\n";
}

echo "\n🛠️ EXTENSION FIXES:\n";
echo "===================\n";

// Check if we can find the PHP installation directory
$phpDir = dirname(PHP_BINARY);
echo "PHP Directory: {$phpDir}\n";

// Check for common extension directories
$possibleExtDirs = [
    $phpDir . '/ext',
    $phpDir . '/../ext',
    'C:/php/ext',
    'C:/php82/ext',
    'C:/php84/ext',
];

$actualExtDir = null;
foreach ($possibleExtDirs as $dir) {
    if (is_dir($dir)) {
        $actualExtDir = $dir;
        echo "Found extension directory: {$dir}\n";
        break;
    }
}

if ($actualExtDir) {
    echo "\n📁 AVAILABLE EXTENSION FILES:\n";
    echo "=============================\n";
    
    $extFiles = glob($actualExtDir . '/*.dll');
    $relevantExts = [];
    
    foreach ($extFiles as $file) {
        $filename = basename($file);
        if (strpos($filename, 'mysql') !== false || 
            strpos($filename, 'sqlite') !== false || 
            strpos($filename, 'fileinfo') !== false ||
            strpos($filename, 'pdo') !== false) {
            $relevantExts[] = $filename;
            echo "Found: {$filename}\n";
        }
    }
    
    if (empty($relevantExts)) {
        echo "❌ No relevant extension files found in {$actualExtDir}\n";
    }
} else {
    echo "❌ Could not locate PHP extension directory\n";
}

echo "\n🔧 RECOMMENDED SOLUTIONS:\n";
echo "=========================\n";

if (!extension_loaded('pdo_mysql')) {
    echo "1. Enable pdo_mysql extension:\n";
    echo "   - Uncomment 'extension=pdo_mysql' in php.ini\n";
    echo "   - Or run: php -d extension=pdo_mysql your_script.php\n\n";
}

if (!extension_loaded('mysqli')) {
    echo "2. Enable mysqli extension:\n";
    echo "   - Uncomment 'extension=mysqli' in php.ini\n";
    echo "   - Or run: php -d extension=mysqli your_script.php\n\n";
}

if (!extension_loaded('fileinfo')) {
    echo "3. Enable fileinfo extension:\n";
    echo "   - Uncomment 'extension=fileinfo' in php.ini\n";
    echo "   - Or run: php -d extension=fileinfo your_script.php\n\n";
}

if (!extension_loaded('sqlite3')) {
    echo "4. Enable sqlite3 extension:\n";
    echo "   - Uncomment 'extension=sqlite3' in php.ini\n";
    echo "   - Or run: php -d extension=sqlite3 your_script.php\n\n";
}

echo "🚀 LARAVEL SERVER COMMANDS:\n";
echo "===========================\n";
echo "Basic (Demo Mode):\n";
echo "php artisan serve --host=127.0.0.1 --port=8000\n\n";

echo "With MySQL Extensions:\n";
echo "php -d extension=pdo_mysql -d extension=mysqli -d extension=fileinfo artisan serve --host=127.0.0.1 --port=8000\n\n";

echo "Full Extensions (Recommended):\n";
echo "php -d extension=pdo_mysql -d extension=mysqli -d extension=fileinfo -d extension=sqlite3 -d extension=pdo_sqlite artisan serve --host=127.0.0.1 --port=8000\n\n";

// Test database connection with extensions loaded
echo "🧪 TESTING DATABASE CONNECTION:\n";
echo "===============================\n";

// Test database connection
try {
    if (extension_loaded('pdo_mysql')) {
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=liquid', 'liquid', 'liquid');
        echo "✅ MySQL Connection: SUCCESS\n";
    } else {
        echo "⚠️ MySQL Connection: Cannot test - pdo_mysql not loaded\n";
    }
} catch (Exception $e) {
    echo "❌ MySQL Connection: FAILED - " . $e->getMessage() . "\n";
}

echo "\n🎯 NEXT STEPS:\n";
echo "==============\n";
echo "1. Run Laravel server with extensions:\n";
echo "   php -d extension=pdo_mysql -d extension=mysqli -d extension=fileinfo artisan serve\n\n";
echo "2. Or modify php.ini to permanently enable extensions\n";
echo "3. Run database migrations: php artisan migrate\n";
echo "4. Seed database: php artisan db:seed\n";

echo "\n✨ LIQUID LIGHTS TICKETS - READY TO SHINE! ✨\n";
