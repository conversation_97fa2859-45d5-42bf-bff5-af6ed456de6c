<?php

namespace App\Http\Controllers;

use App\Services\SEOService;
use Illuminate\Http\Response;

class SEOController extends Controller
{
    protected SEOService $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Generate and serve sitemap.xml
     */
    public function sitemap(): Response
    {
        $sitemap = $this->seoService->getCachedSitemap();

        return response($sitemap, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Generate and serve robots.txt
     */
    public function robots(): Response
    {
        $robots = $this->seoService->generateRobotsTxt();

        return response($robots, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'public, max-age=86400',
        ]);
    }
}
