<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Features;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Initialize feature flags system
        Features::define();

        // Register Blade directives for feature flags
        $this->registerBladeDirectives();
    }

    /**
     * Register custom Blade directives
     */
    private function registerBladeDirectives(): void
    {
        // @feature directive
        Blade::if('feature', function ($feature) {
            return Features::enabled($feature);
        });

        // @featureany directive (any of the features enabled)
        Blade::if('featureany', function (...$features) {
            foreach ($features as $feature) {
                if (Features::enabled($feature)) {
                    return true;
                }
            }
            return false;
        });

        // @featureall directive (all features enabled)
        Blade::if('featureall', function (...$features) {
            foreach ($features as $feature) {
                if (!Features::enabled($feature)) {
                    return false;
                }
            }
            return true;
        });
    }
}
