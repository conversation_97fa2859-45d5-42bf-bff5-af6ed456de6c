<p align="center"><img src="/art/logo.svg" alt="Laravel Pennant Package Logo"></p>

<p align="center">
<a href="https://github.com/laravel/pennant/actions"><img src="https://github.com/laravel/pennant/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/pennant"><img src="https://img.shields.io/packagist/dt/laravel/pennant" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/pennant"><img src="https://img.shields.io/packagist/v/laravel/pennant" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/pennant"><img src="https://img.shields.io/packagist/l/laravel/pennant" alt="License"></a>
</p>

## Introduction

Laravel Pennant is a simple, lightweight library for managing feature flags.

## Official Documentation

Documentation for Laravel Pennant can be found on the [Laravel website](https://laravel.com/docs/pennant).

## Contributing

Thank you for considering contributing to Laravel Pennant! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

Please review [our security policy](https://github.com/laravel/pennant/security/policy) on how to report security vulnerabilities.

## License

Laravel Pennant is open-sourced software licensed under the [MIT license](LICENSE.md).
