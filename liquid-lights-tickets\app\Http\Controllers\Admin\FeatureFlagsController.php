<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Features;
use App\Models\Feature;

class FeatureFlagsController extends Controller
{
    /**
     * Display feature flags management page
     */
    public function index()
    {
        $featureStatus = Features::getFeatureStatus();
        
        return view('admin.feature-flags.index', [
            'featureStatus' => $featureStatus,
        ]);
    }

    /**
     * Toggle a feature flag
     */
    public function toggle(Request $request)
    {
        $request->validate([
            'feature' => 'required|string',
            'enabled' => 'required|boolean',
        ]);

        $feature = $request->input('feature');
        $enabled = $request->input('enabled');

        if ($enabled) {
            Features::enable($feature);
        } else {
            Features::disable($feature);
        }

        return response()->json([
            'success' => true,
            'message' => "Feature '{$feature}' has been " . ($enabled ? 'enabled' : 'disabled'),
            'feature' => $feature,
            'enabled' => $enabled,
        ]);
    }

    /**
     * Bulk toggle features
     */
    public function bulkToggle(Request $request)
    {
        $request->validate([
            'features' => 'required|array',
            'features.*.feature' => 'required|string',
            'features.*.enabled' => 'required|boolean',
        ]);

        $updated = [];
        
        foreach ($request->input('features') as $featureData) {
            $feature = $featureData['feature'];
            $enabled = $featureData['enabled'];

            if ($enabled) {
                Features::enable($feature);
            } else {
                Features::disable($feature);
            }

            $updated[] = [
                'feature' => $feature,
                'enabled' => $enabled,
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Features updated successfully',
            'updated' => $updated,
        ]);
    }

    /**
     * Reset all features to default
     */
    public function reset()
    {
        // Delete all feature overrides
        Feature::truncate();
        
        // Clear cache
        Features::clearCache();

        return response()->json([
            'success' => true,
            'message' => 'All features have been reset to default values',
        ]);
    }

    /**
     * Get feature status as JSON
     */
    public function status()
    {
        $featureStatus = Features::getFeatureStatus();
        
        return response()->json([
            'success' => true,
            'features' => $featureStatus,
        ]);
    }

    /**
     * Export feature configuration
     */
    public function export()
    {
        $features = Feature::all();
        $config = [];

        foreach ($features as $feature) {
            $config[$feature->name] = $feature->value;
        }

        return response()->json([
            'success' => true,
            'config' => $config,
            'exported_at' => now()->toISOString(),
        ]);
    }

    /**
     * Import feature configuration
     */
    public function import(Request $request)
    {
        $request->validate([
            'config' => 'required|array',
        ]);

        $config = $request->input('config');
        $imported = [];

        foreach ($config as $featureName => $value) {
            Feature::updateOrCreate(
                ['name' => $featureName, 'scope' => 'global'],
                ['value' => $value]
            );
            
            $imported[] = $featureName;
        }

        // Clear cache
        Features::clearCache();

        return response()->json([
            'success' => true,
            'message' => 'Feature configuration imported successfully',
            'imported' => $imported,
        ]);
    }

    /**
     * Get feature usage analytics
     */
    public function analytics()
    {
        $features = Feature::all();
        $analytics = [];

        foreach ($features as $feature) {
            $analytics[] = [
                'name' => $feature->name,
                'enabled' => $feature->value['enabled'] ?? false,
                'updated_at' => $feature->updated_at,
                'scope' => $feature->scope,
            ];
        }

        return response()->json([
            'success' => true,
            'analytics' => $analytics,
        ]);
    }
}
