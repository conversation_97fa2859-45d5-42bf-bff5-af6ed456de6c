<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PublicController;

// SEO Routes
Route::get('/sitemap.xml', [\App\Http\Controllers\SEOController::class, 'sitemap'])->name('sitemap');
Route::get('/robots.txt', [\App\Http\Controllers\SEOController::class, 'robots'])->name('robots');

// Public Routes
Route::get('/', [PublicController::class, 'index'])->name('public.index');
Route::get('/events', [PublicController::class, 'events'])->name('public.events');
Route::get('/events/{id}', [PublicController::class, 'event'])->name('public.event');
Route::get('/artists', function() { return view('public.artists.index'); })->name('public.artists');
Route::get('/artists/{id}', [PublicController::class, 'artist'])->name('public.artist');
Route::get('/search', [PublicController::class, 'search'])->name('public.search');
Route::get('/offline', function () {
    return view('offline');
})->name('offline');

// Authentication Routes
use App\Http\Controllers\UserAuthController;
Route::get('/login', [UserAuthController::class, 'showLogin'])->name('login');
Route::post('/login', [UserAuthController::class, 'login']);
Route::get('/register', [UserAuthController::class, 'showRegister'])->name('register');
Route::post('/register', [UserAuthController::class, 'register']);
Route::post('/logout', [UserAuthController::class, 'logout'])->name('logout');
Route::get('/forgot-password', [UserAuthController::class, 'showForgotPassword'])->name('forgot-password');
Route::post('/forgot-password', [UserAuthController::class, 'forgotPassword']);
Route::get('/verify-email/{id}/{hash}', [UserAuthController::class, 'verifyEmail'])->name('verification.verify');
Route::post('/email/verification-notification', [UserAuthController::class, 'resendVerification'])->name('verification.send')->middleware('auth');

// OTPless Authentication Routes
Route::prefix('auth/otpless')->group(function () {
    Route::post('/initiate', [OTPlessController::class, 'initiate'])->name('auth.otpless.initiate');
    Route::post('/verify', [OTPlessController::class, 'verify'])->name('auth.otpless.verify');
    Route::get('/callback', [OTPlessController::class, 'callback'])->name('auth.otpless.callback');
});

// Alternative OTPless Login Route
Route::get('/otpless-login', [OTPlessController::class, 'showLogin'])->name('otpless.login');

// Booking Routes
use App\Http\Controllers\BookingController;
Route::get('/cart', [BookingController::class, 'cart'])->name('booking.cart');
Route::get('/checkout', [BookingController::class, 'checkout'])->name('booking.checkout');
Route::post('/api/booking/process', [BookingController::class, 'processBooking'])->name('booking.process');
Route::get('/booking/{id}/confirmation', [BookingController::class, 'confirmation'])->name('booking.confirmation');
Route::post('/api/booking/{id}/cancel', [BookingController::class, 'cancel'])->name('booking.cancel');

// User Dashboard Routes
use App\Http\Controllers\UserDashboardController;
Route::middleware('auth')->group(function () {
    Route::get('/user/dashboard', [UserDashboardController::class, 'dashboard'])->name('user.dashboard');
    Route::get('/user/bookings', [UserDashboardController::class, 'bookings'])->name('user.bookings');
    Route::get('/user/bookings/{id}', [UserDashboardController::class, 'booking'])->name('user.booking');
    Route::get('/user/bookings/{id}/download', [UserDashboardController::class, 'downloadTicket'])->name('user.booking.download');
    Route::get('/user/profile', [UserDashboardController::class, 'profile'])->name('user.profile');
    Route::put('/user/profile', [UserDashboardController::class, 'updateProfile'])->name('user.profile.update');
    Route::put('/user/password', [UserDashboardController::class, 'updatePassword'])->name('user.password.update');
    Route::get('/user/notifications', function () {
        return view('user.notifications');
    })->name('user.notifications');
});

// Payment Routes
use App\Http\Controllers\PaymentController;
Route::middleware('auth')->group(function () {
    Route::post('/api/payment/create-intent', [PaymentController::class, 'createPaymentIntent'])->name('payment.create-intent');
    Route::post('/api/payment/success', [PaymentController::class, 'handlePaymentSuccess'])->name('payment.success');
    Route::post('/api/payment/failure', [PaymentController::class, 'handlePaymentFailure'])->name('payment.failure');
    Route::get('/api/payment/methods', [PaymentController::class, 'getPaymentMethods'])->name('payment.methods');
});

// Webhook Routes (no auth required)
use App\Http\Controllers\WebhookController;
Route::post('/webhooks/razorpay', [WebhookController::class, 'razorpay'])->name('webhooks.razorpay');
Route::post('/webhooks/stripe', [WebhookController::class, 'stripe'])->name('webhooks.stripe');
Route::post('/webhooks/test', [WebhookController::class, 'test'])->name('webhooks.test');

// Check-in Routes (Admin only)
use App\Http\Controllers\CheckInController;
Route::middleware(['auth', 'role:admin'])->prefix('admin')->group(function () {
    Route::get('/checkin/{eventId?}', [CheckInController::class, 'scanner'])->name('admin.checkin.scanner');
    Route::post('/checkin/validate', [CheckInController::class, 'validateQR'])->name('admin.checkin.validate');
    Route::post('/checkin/checkin', [CheckInController::class, 'checkIn'])->name('admin.checkin.checkin');
    Route::post('/checkin/manual', [CheckInController::class, 'manualCheckIn'])->name('admin.checkin.manual');
    Route::get('/checkin/events/{eventId}/stats', [CheckInController::class, 'getEventStats'])->name('admin.checkin.stats');
    Route::get('/checkin/events/{eventId}/recent', [CheckInController::class, 'getRecentCheckIns'])->name('admin.checkin.recent');
});

// Push Notification Routes
use App\Http\Controllers\PushNotificationController;
Route::middleware('auth')->prefix('api/notifications')->group(function () {
    Route::get('/vapid-key', [PushNotificationController::class, 'getVapidPublicKey'])->name('notifications.vapid-key');
    Route::post('/subscribe', [PushNotificationController::class, 'subscribe'])->name('notifications.subscribe');
    Route::post('/unsubscribe', [PushNotificationController::class, 'unsubscribe'])->name('notifications.unsubscribe');
    Route::get('/status', [PushNotificationController::class, 'getSubscriptionStatus'])->name('notifications.status');
    Route::post('/test', [PushNotificationController::class, 'sendTestNotification'])->name('notifications.test');
    Route::get('/preferences', [PushNotificationController::class, 'getPreferences'])->name('notifications.preferences');
    Route::post('/preferences', [PushNotificationController::class, 'updatePreferences'])->name('notifications.preferences.update');
});

// Admin Web Routes
use App\Http\Controllers\Admin\SponsorController;
use App\Http\Controllers\Admin\SponsorshipTierController;
use App\Http\Controllers\Admin\EventSponsorController;
use App\Http\Controllers\Admin\SponsorAnalyticsController;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Auth\OTPlessController;

Route::prefix('admin')->name('admin.')->group(function () {
    // Admin authentication routes
    Route::get('/login', [AdminAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AdminAuthController::class, 'login']);
    Route::post('/logout', [AdminAuthController::class, 'logout'])->name('logout');

    // Debug route to test authentication
    Route::get('/debug', function () {
        $user = Auth::user();
        return response()->json([
            'authenticated' => Auth::check(),
            'user' => $user ? [
                'id' => $user->id,
                'email' => $user->email,
                'role' => $user->role,
                'is_active' => $user->is_active
            ] : null,
            'session_id' => session()->getId(),
            'csrf_token' => csrf_token()
        ]);
    });

    // Test dashboard without middleware
    Route::get('/dashboard-test', function () {
        return view('admin.dashboard', [
            'kpis' => [
                'total_revenue' => ['value' => 0, 'formatted' => '₹0.00', 'growth' => 0],
                'total_bookings' => ['value' => 0, 'formatted' => '0'],
                'total_tickets' => ['value' => 0, 'formatted' => '0'],
                'active_events' => ['value' => 0, 'formatted' => '0'],
                'new_users' => ['value' => 0, 'formatted' => '0'],
                'avg_ticket_price' => ['value' => 0, 'formatted' => '₹0.00']
            ],
            'charts' => [
                'daily_revenue' => [],
                'event_popularity' => [],
                'ticket_type_distribution' => []
            ],
            'recent_activities' => [],
            'upcoming_events' => [],
        ]);
    });

    // Protected admin routes (require authentication)
    Route::middleware(['admin.auth'])->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

        Route::get('/events', function () {
            return view('admin.events.index');
        })->name('events.index');

        Route::get('/events/create', function () {
            return view('admin.events.create');
        })->name('events.create');

        Route::get('/events/{id}', function ($id) {
            return view('admin.events.show', compact('id'));
        })->name('events.show');

        Route::get('/events/{id}/edit', function ($id) {
            return view('admin.events.edit', compact('id'));
        })->name('events.edit');

        Route::get('/bookings', function () {
            return view('admin.bookings.index');
        })->name('bookings.index');

        Route::get('/bookings/create', function () {
            return view('admin.bookings.create');
        })->name('bookings.create');

        Route::get('/bookings/{id}', function ($id) {
            return view('admin.bookings.show', compact('id'));
        })->name('bookings.show');

        Route::get('/users', function () {
            return view('admin.users.index');
        })->name('users.index');

        Route::get('/users/create', function () {
            return view('admin.users.create');
        })->name('users.create');

        Route::get('/users/{id}', function ($id) {
            return view('admin.users.show', compact('id'));
        })->name('users.show');

        Route::get('/users/{id}/edit', function ($id) {
            return view('admin.users.edit', compact('id'));
        })->name('users.edit');

        Route::get('/profile', function () {
            return view('admin.profile');
        })->name('profile');

        Route::get('/settings', function () {
            return view('admin.settings');
        })->name('settings');

        Route::get('/analytics', function () {
            return view('admin.analytics');
        })->name('analytics');

        // Sponsors Management
        Route::resource('sponsors', SponsorController::class);
        Route::resource('sponsorship-tiers', SponsorshipTierController::class);
        Route::resource('event-sponsors', EventSponsorController::class);

        // Sponsor Analytics
        Route::get('/sponsor-analytics', [SponsorAnalyticsController::class, 'index'])->name('sponsor-analytics.index');
        Route::get('/sponsor-analytics/sponsor/{sponsor}', [SponsorAnalyticsController::class, 'sponsorReport'])->name('sponsor-analytics.sponsor-report');
        Route::get('/sponsor-analytics/export', [SponsorAnalyticsController::class, 'export'])->name('sponsor-analytics.export');
        Route::get('/sponsor-analytics/chart-data', [SponsorAnalyticsController::class, 'chartData'])->name('sponsor-analytics.chart-data');

        // Feature Flags Routes
        Route::get('/feature-flags', [\App\Http\Controllers\Admin\FeatureFlagsController::class, 'index'])->name('feature-flags.index');
        Route::post('/feature-flags/toggle', [\App\Http\Controllers\Admin\FeatureFlagsController::class, 'toggle'])->name('feature-flags.toggle');
        Route::post('/feature-flags/bulk-toggle', [\App\Http\Controllers\Admin\FeatureFlagsController::class, 'bulkToggle'])->name('feature-flags.bulk-toggle');
        Route::post('/feature-flags/reset', [\App\Http\Controllers\Admin\FeatureFlagsController::class, 'reset'])->name('feature-flags.reset');
        Route::get('/feature-flags/status', [\App\Http\Controllers\Admin\FeatureFlagsController::class, 'status'])->name('feature-flags.status');
        Route::get('/feature-flags/export', [\App\Http\Controllers\Admin\FeatureFlagsController::class, 'export'])->name('feature-flags.export');
        Route::post('/feature-flags/import', [\App\Http\Controllers\Admin\FeatureFlagsController::class, 'import'])->name('feature-flags.import');
        Route::get('/feature-flags/analytics', [\App\Http\Controllers\Admin\FeatureFlagsController::class, 'analytics'])->name('feature-flags.analytics');

        // Performance Monitoring Routes
        Route::get('/performance', [\App\Http\Controllers\Admin\PerformanceController::class, 'index'])->name('performance.index');
        Route::get('/performance/metrics', [\App\Http\Controllers\Admin\PerformanceController::class, 'metrics'])->name('performance.metrics');
        Route::post('/performance/clear-caches', [\App\Http\Controllers\Admin\PerformanceController::class, 'clearCaches'])->name('performance.clear-caches');
        Route::post('/performance/warm-up-caches', [\App\Http\Controllers\Admin\PerformanceController::class, 'warmUpCaches'])->name('performance.warm-up-caches');
        Route::post('/performance/optimize-images', [\App\Http\Controllers\Admin\PerformanceController::class, 'optimizeImages'])->name('performance.optimize-images');
        Route::post('/performance/clear-old-metrics', [\App\Http\Controllers\Admin\PerformanceController::class, 'clearOldMetrics'])->name('performance.clear-old-metrics');
        Route::get('/performance/cache-stats', [\App\Http\Controllers\Admin\PerformanceController::class, 'cacheStats'])->name('performance.cache-stats');
        Route::post('/performance/invalidate-cache', [\App\Http\Controllers\Admin\PerformanceController::class, 'invalidateCache'])->name('performance.invalidate-cache');
        Route::post('/performance/run-tests', [\App\Http\Controllers\Admin\PerformanceController::class, 'runTests'])->name('performance.run-tests');
        Route::get('/performance/export-report', [\App\Http\Controllers\Admin\PerformanceController::class, 'exportReport'])->name('performance.export-report');

    });
});
