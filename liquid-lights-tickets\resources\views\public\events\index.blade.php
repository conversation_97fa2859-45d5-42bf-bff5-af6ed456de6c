@extends('public.layout')

@section('title', 'Events - Liquid Lights')
@section('description', 'Discover all upcoming events, concerts, and nightlife experiences. Book tickets for the hottest events in the city.')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 bg-gradient-to-br from-black via-purple-900/50 to-black overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-32 h-32 border border-blue-400 rounded-full"></div>
        <div class="absolute top-40 right-20 w-24 h-24 border border-purple-400 rounded-full"></div>
        <div class="absolute bottom-20 left-1/4 w-16 h-16 border border-pink-400 rounded-full"></div>
    </div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-bold font-display mb-6">
            <span class="neon-text">All</span> 
            <span class="text-white">Events</span>
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Discover the hottest events, exclusive parties, and unforgettable experiences happening in your city.
        </p>
        
        <!-- Search Bar -->
        <div class="max-w-2xl mx-auto">
            <form method="GET" action="{{ route('public.events') }}" class="glass-card p-4">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" 
                               name="search" 
                               value="{{ request('search') }}"
                               placeholder="Search events, venues, artists..."
                               class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                    </div>
                    <div class="md:w-48">
                        <select name="category" 
                                class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                            <option value="">All Categories</option>
                            <option value="concert" {{ request('category') === 'concert' ? 'selected' : '' }}>Concerts</option>
                            <option value="party" {{ request('category') === 'party' ? 'selected' : '' }}>Parties</option>
                            <option value="club" {{ request('category') === 'club' ? 'selected' : '' }}>Club Events</option>
                            <option value="festival" {{ request('category') === 'festival' ? 'selected' : '' }}>Festivals</option>
                        </select>
                    </div>
                    <button type="submit" class="btn-primary px-6 py-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Events Grid -->
<section class="py-16 bg-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($events->count() > 0)
            <!-- Results Info -->
            <div class="flex items-center justify-between mb-8">
                <div class="text-gray-400">
                    Showing {{ $events->firstItem() }}-{{ $events->lastItem() }} of {{ $events->total() }} events
                </div>
                @if(request()->hasAny(['search', 'category', 'date', 'price']))
                    <a href="{{ route('public.events') }}" 
                       class="text-blue-400 hover:text-blue-300 text-sm font-medium">
                        Clear Filters
                    </a>
                @endif
            </div>

            <!-- Events Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                @foreach($events as $event)
                    <div class="event-card group">
                        <!-- Event Image -->
                        <div class="relative overflow-hidden h-64">
                            @if($event->featured_image)
                                <img src="{{ Storage::url($event->featured_image) }}" 
                                     alt="{{ $event->title }}"
                                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            @else
                                <div class="w-full h-full bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Event Date Badge -->
                            <div class="absolute top-4 left-4 glass-card px-3 py-2">
                                <div class="text-center">
                                    <div class="text-xs font-medium text-gray-300">
                                        {{ \Carbon\Carbon::parse($event->event_date)->format('M') }}
                                    </div>
                                    <div class="text-lg font-bold text-white">
                                        {{ \Carbon\Carbon::parse($event->event_date)->format('d') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Price Badge -->
                            <div class="absolute top-4 right-4 bg-gradient-to-r from-green-500 to-blue-500 px-3 py-1 rounded-full">
                                <div class="text-sm font-bold text-white">
                                    @if($event->ticketTypes && $event->ticketTypes->count() > 0)
                                        ₹{{ number_format($event->ticketTypes->min('price')) }}
                                        @if($event->ticketTypes->min('price') != $event->ticketTypes->max('price'))
                                            +
                                        @endif
                                    @else
                                        TBA
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Event Details -->
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors">
                                {{ $event->title }}
                            </h3>
                            
                            <p class="text-gray-400 text-sm mb-4 line-clamp-2">
                                {{ Str::limit($event->description, 100) }}
                            </p>

                            <!-- Event Meta -->
                            <div class="space-y-2 mb-6">
                                <div class="flex items-center text-sm text-gray-400">
                                    <svg class="w-4 h-4 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ \Carbon\Carbon::parse($event->event_date)->format('M d, Y') }} at {{ $event->event_time }}
                                </div>
                                <div class="flex items-center text-sm text-gray-400">
                                    <svg class="w-4 h-4 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ $event->venue_name }}
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-3">
                                <a href="{{ route('public.events.show', $event->slug) }}" 
                                   class="btn-primary flex-1 justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                                    </svg>
                                    Book Now
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="flex justify-center">
                {{ $events->links() }}
            </div>
        @else
            <!-- No Events State -->
            <div class="text-center py-16">
                <div class="glass-card p-12 max-w-md mx-auto">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-white mb-2">No Events Found</h3>
                    <p class="text-gray-400 mb-6">
                        @if(request()->hasAny(['search', 'category', 'date', 'price']))
                            Try adjusting your filters or search terms.
                        @else
                            Check back soon for exciting new events!
                        @endif
                    </p>
                    @if(request()->hasAny(['search', 'category', 'date', 'price']))
                        <a href="{{ route('public.events') }}" class="btn-primary">
                            Clear Filters
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>
</section>
@endsection
