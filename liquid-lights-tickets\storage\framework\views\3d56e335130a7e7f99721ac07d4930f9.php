<?php $__env->startSection('title', 'Analytics'); ?>

<?php $__env->startSection('content'); ?>
<div x-data="analyticsManager">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="sm:flex sm:items-center sm:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Analytics</h1>
                <p class="mt-1 text-sm text-gray-600">Comprehensive insights into your event performance and business metrics.</p>
            </div>
            <div class="mt-4 sm:mt-0 flex space-x-3">
                <div class="flex space-x-2">
                    <input x-model="dateRange.start" 
                           @change="loadAnalytics()"
                           type="date" 
                           class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <input x-model="dateRange.end" 
                           @change="loadAnalytics()"
                           type="date" 
                           class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>
                <button @click="exportAnalytics()" 
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export
                </button>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Analytics Content -->
    <div x-show="!loading" class="space-y-8">
        <!-- Overview Stats with Growth -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900" x-text="`₹${analytics.overview?.current?.total_revenue || 0}`"></div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold" 
                                         :class="analytics.overview?.growth?.total_revenue >= 0 ? 'text-green-600' : 'text-red-600'">
                                        <span x-text="analytics.overview?.growth?.total_revenue ? (analytics.overview.growth.total_revenue > 0 ? '+' : '') + analytics.overview.growth.total_revenue + '%' : ''"></span>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Bookings</dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900" x-text="analytics.overview?.current?.total_bookings || 0"></div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold" 
                                         :class="analytics.overview?.growth?.total_bookings >= 0 ? 'text-green-600' : 'text-red-600'">
                                        <span x-text="analytics.overview?.growth?.total_bookings ? (analytics.overview.growth.total_bookings > 0 ? '+' : '') + analytics.overview.growth.total_bookings + '%' : ''"></span>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h10a2 2 0 012 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Tickets Sold</dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900" x-text="analytics.overview?.current?.total_tickets || 0"></div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold" 
                                         :class="analytics.overview?.growth?.total_tickets >= 0 ? 'text-green-600' : 'text-red-600'">
                                        <span x-text="analytics.overview?.growth?.total_tickets ? (analytics.overview.growth.total_tickets > 0 ? '+' : '') + analytics.overview.growth.total_tickets + '%' : ''"></span>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">New Users</dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900" x-text="analytics.overview?.current?.new_users || 0"></div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold" 
                                         :class="analytics.overview?.growth?.new_users >= 0 ? 'text-green-600' : 'text-red-600'">
                                        <span x-text="analytics.overview?.growth?.new_users ? (analytics.overview.growth.new_users > 0 ? '+' : '') + analytics.overview.growth.new_users + '%' : ''"></span>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Revenue Trends -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Trends</h3>
                <canvas id="revenueTrendsChart" width="400" height="200"></canvas>
            </div>

            <!-- Event Performance -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Top Events by Revenue</h3>
                <canvas id="eventPerformanceChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Additional Analytics -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- User Growth -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
                <canvas id="userGrowthChart" width="300" height="200"></canvas>
            </div>

            <!-- Ticket Type Performance -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Ticket Type Performance</h3>
                <div class="space-y-3">
                    <template x-for="ticketType in analytics.ticket_analytics?.ticket_type_performance?.slice(0, 5) || []" :key="ticketType.id">
                        <div class="flex justify-between items-center">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900" x-text="ticketType.name"></div>
                                <div class="text-sm text-gray-500" x-text="`${ticketType.tickets_sold || 0} tickets sold`"></div>
                            </div>
                            <div class="text-sm font-medium text-gray-900" x-text="`₹${ticketType.revenue || 0}`"></div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Top Customers -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Top Customers</h3>
                <div class="space-y-3">
                    <template x-for="customer in analytics.user_analytics?.top_customers?.slice(0, 5) || []" :key="customer.id">
                        <div class="flex justify-between items-center">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900" x-text="customer.name"></div>
                                <div class="text-sm text-gray-500" x-text="`${customer.booking_count || 0} bookings`"></div>
                            </div>
                            <div class="text-sm font-medium text-gray-900" x-text="`₹${customer.total_spent || 0}`"></div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- Upcoming Events Performance -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Upcoming Events Performance</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Venue</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales Progress</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue Potential</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="event in analytics.event_performance?.upcoming_events || []" :key="event.id">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900" x-text="event.title"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900" x-text="new Date(event.date).toLocaleDateString()"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900" x-text="event.venue"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-1">
                                            <div class="text-sm text-gray-900" x-text="`${event.sold_tickets}/${event.total_tickets} (${event.sales_percentage}%)`"></div>
                                            <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                                <div class="bg-blue-600 h-2 rounded-full" :style="`width: ${event.sales_percentage}%`"></div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">High</div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\LLe\liquid-lights-tickets\resources\views/admin/analytics.blade.php ENDPATH**/ ?>