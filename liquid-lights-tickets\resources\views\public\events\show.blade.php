@extends('public.layout')

@section('title', $event->title . ' - Liquid Lights Tickets')
@section('description', $event->short_description)

@section('content')
<div x-data="ticketSelector({{ $event->id }}, {{ $event->ticketTypes->to<PERSON>son() }})">
    <!-- Event Hero Section -->
    <section class="relative">
        <div class="relative h-96 md:h-[500px] overflow-hidden">
            <img src="{{ $event->banner_image ?: '/images/event-placeholder.jpg' }}" 
                 alt="{{ $event->title }}" 
                 class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent"></div>
            
            <!-- Event Info Overlay -->
            <div class="absolute bottom-0 left-0 right-0 p-8">
                <div class="max-w-7xl mx-auto">
                    <div class="text-white">
                        @if($event->is_featured)
                        <span class="inline-block bg-yellow-500 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold mb-4">
                            Featured Event
                        </span>
                        @endif
                        
                        <h1 class="text-4xl md:text-6xl font-bold mb-4">{{ $event->title }}</h1>
                        
                        <div class="flex flex-col md:flex-row md:items-center gap-4 md:gap-8 text-lg">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                {{ \Carbon\Carbon::parse($event->event_date)->format('l, F d, Y') }}
                            </div>
                            
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ \Carbon\Carbon::parse($event->event_time)->format('g:i A') }}
                            </div>
                            
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                {{ $event->venue_name }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <!-- Left Column - Event Details -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Description -->
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Event</h2>
                        <div class="prose prose-lg max-w-none text-gray-700">
                            {!! nl2br(e($event->description)) !!}
                        </div>
                    </div>

                    <!-- Artists -->
                    @if($event->artists->count() > 0)
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Featured Artists</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($event->artists as $artist)
                            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                                <img src="{{ $artist->image ?: '/images/artist-placeholder.jpg' }}" 
                                     alt="{{ $artist->name }}" 
                                     class="w-16 h-16 rounded-full object-cover">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900">{{ $artist->name }}</h3>
                                    @if($artist->bio)
                                    <p class="text-gray-600 text-sm mt-1">{{ Str::limit($artist->bio, 100) }}</p>
                                    @endif
                                    <div class="flex space-x-3 mt-2">
                                        @if($artist->instagram_handle)
                                        <a href="https://instagram.com/{{ $artist->instagram_handle }}" 
                                           target="_blank" 
                                           class="text-pink-600 hover:text-pink-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                                            </svg>
                                        </a>
                                        @endif
                                        @if($artist->spotify_link)
                                        <a href="{{ $artist->spotify_link }}" 
                                           target="_blank" 
                                           class="text-green-600 hover:text-green-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
                                            </svg>
                                        </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Venue Information -->
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Venue Information</h2>
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="font-semibold text-gray-900 mb-2">{{ $event->venue_name }}</h3>
                            <div class="text-gray-700 whitespace-pre-line">{{ $event->venue_address }}</div>
                            
                            <!-- Map placeholder -->
                            <div class="mt-4 h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                                <div class="text-center text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <p>Interactive map coming soon</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Ticket Selection -->
                <div class="lg:col-span-1">
                    <div class="sticky top-24">
                        <div class="bg-white border border-gray-200 rounded-xl shadow-lg p-6">
                            <h2 class="text-xl font-bold text-gray-900 mb-6">Select Tickets</h2>
                            
                            @if($event->ticketTypes->count() > 0)
                                <div class="space-y-4">
                                    @foreach($event->ticketTypes as $ticketType)
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex justify-between items-start mb-2">
                                            <div>
                                                <h3 class="font-semibold text-gray-900">{{ $ticketType->name }}</h3>
                                                <p class="text-2xl font-bold text-blue-600">₹{{ number_format($ticketType->price) }}</p>
                                            </div>
                                            <div class="text-right text-sm text-gray-500">
                                                {{ $ticketType->quantity_available - $ticketType->quantity_sold }} left
                                            </div>
                                        </div>
                                        
                                        @if($ticketType->quantity_available - $ticketType->quantity_sold > 0)
                                        <div class="flex items-center justify-between mt-4">
                                            <span class="text-sm text-gray-700">Quantity:</span>
                                            <div class="flex items-center space-x-3">
                                                <button @click="updateQuantity({{ $ticketType->id }}, selectedTickets[{{ $ticketType->id }}] - 1)"
                                                        :disabled="selectedTickets[{{ $ticketType->id }}] <= 0"
                                                        class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                                    </svg>
                                                </button>
                                                <span class="w-8 text-center font-semibold" x-text="selectedTickets[{{ $ticketType->id }}]"></span>
                                                <button @click="updateQuantity({{ $ticketType->id }}, selectedTickets[{{ $ticketType->id }}] + 1)"
                                                        :disabled="selectedTickets[{{ $ticketType->id }}] >= getAvailableQuantity({{ $ticketType->id }})"
                                                        class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        @else
                                        <div class="mt-4 text-center">
                                            <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold">
                                                Sold Out
                                            </span>
                                        </div>
                                        @endif
                                    </div>
                                    @endforeach
                                </div>

                                <!-- Order Summary -->
                                <div x-show="totalTickets > 0" class="mt-6 pt-6 border-t border-gray-200">
                                    <div class="space-y-2 mb-4">
                                        <div class="flex justify-between text-sm">
                                            <span>Total Tickets:</span>
                                            <span x-text="totalTickets"></span>
                                        </div>
                                        <div class="flex justify-between text-lg font-bold">
                                            <span>Total Amount:</span>
                                            <span x-text="`₹${totalAmount.toLocaleString()}`"></span>
                                        </div>
                                    </div>
                                    
                                    @auth
                                    <button @click="addToCart()" 
                                            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold transition-colors">
                                        Add to Cart
                                    </button>
                                    @else
                                    <div class="space-y-3">
                                        <p class="text-sm text-gray-600 text-center">Please sign in to book tickets</p>
                                        <a href="{{ route('login') }}" 
                                           class="block w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold text-center transition-colors">
                                            Sign In to Book
                                        </a>
                                    </div>
                                    @endauth
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h10a2 2 0 012 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Tickets Available</h3>
                                    <p class="text-gray-600">Tickets for this event are not currently available for sale.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Event Sponsors -->
    @if($event->hasSponsors())
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Our Sponsors</h2>

            @php
                $sponsorsByTier = $event->getSponsorsByTier();
            @endphp

            @foreach($sponsorsByTier as $tierName => $sponsors)
            <div class="mb-12 last:mb-0">
                <h3 class="text-xl font-semibold text-gray-800 mb-6 text-center">{{ $tierName }}</h3>

                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-{{ $sponsors->first()->sponsorshipTier->slug === 'title-sponsor' ? '1' : ($sponsors->first()->sponsorshipTier->slug === 'platinum-sponsor' ? '2' : '4') }} gap-8 items-center justify-items-center">
                    @foreach($sponsors as $eventSponsor)
                    <div class="group">
                        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300 {{ $sponsors->first()->sponsorshipTier->slug === 'title-sponsor' ? 'max-w-md mx-auto' : '' }}">
                            @if($eventSponsor->sponsor->logo)
                                <img src="{{ $eventSponsor->sponsor->logo_url }}"
                                     alt="{{ $eventSponsor->sponsor->name }}"
                                     class="w-full h-{{ $sponsors->first()->sponsorshipTier->slug === 'title-sponsor' ? '24' : '16' }} object-contain mb-4">
                            @else
                                <div class="w-full h-{{ $sponsors->first()->sponsorshipTier->slug === 'title-sponsor' ? '24' : '16' }} bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                                    <span class="text-gray-500 font-medium">{{ $eventSponsor->sponsor->name }}</span>
                                </div>
                            @endif

                            <div class="text-center">
                                <h4 class="font-semibold text-gray-900 mb-2">{{ $eventSponsor->sponsor->name }}</h4>

                                @if($eventSponsor->sponsor->description)
                                    <p class="text-sm text-gray-600 mb-3 line-clamp-2">{{ $eventSponsor->sponsor->description }}</p>
                                @endif

                                @if($eventSponsor->sponsor->website_url)
                                    <a href="{{ $eventSponsor->sponsor->formatted_website_url }}"
                                       target="_blank"
                                       class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium">
                                        Visit Website
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                        </svg>
                                    </a>
                                @endif

                                <!-- Social Media Links -->
                                @if($eventSponsor->sponsor->social_links && array_filter($eventSponsor->sponsor->social_links))
                                    <div class="flex justify-center space-x-3 mt-3">
                                        @if($eventSponsor->sponsor->social_links['facebook'])
                                            <a href="{{ $eventSponsor->sponsor->social_links['facebook'] }}"
                                               target="_blank"
                                               class="text-gray-400 hover:text-blue-600">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                                </svg>
                                            </a>
                                        @endif

                                        @if($eventSponsor->sponsor->social_links['twitter'])
                                            <a href="{{ $eventSponsor->sponsor->social_links['twitter'] }}"
                                               target="_blank"
                                               class="text-gray-400 hover:text-blue-400">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                                </svg>
                                            </a>
                                        @endif

                                        @if($eventSponsor->sponsor->social_links['instagram'])
                                            <a href="{{ $eventSponsor->sponsor->social_links['instagram'] }}"
                                               target="_blank"
                                               class="text-gray-400 hover:text-pink-600">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.928-.875 2.026-1.365 3.323-1.365s2.448.49 3.323 1.365c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.608c-.384 0-.734-.15-.993-.409-.259-.259-.409-.609-.409-.993 0-.384.15-.734.409-.993.259-.259.609-.409.993-.409s.734.15.993.409c.259.259.409.609.409.993 0 .384-.15.734-.409.993-.259.259-.609.409-.993.409z"/>
                                                </svg>
                                            </a>
                                        @endif

                                        @if($eventSponsor->sponsor->social_links['linkedin'])
                                            <a href="{{ $eventSponsor->sponsor->social_links['linkedin'] }}"
                                               target="_blank"
                                               class="text-gray-400 hover:text-blue-700">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                                </svg>
                                            </a>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endforeach
        </div>
    </section>
    @endif

    <!-- Related Events -->
    @if($relatedEvents->count() > 0)
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">You Might Also Like</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedEvents as $relatedEvent)
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <img src="{{ $relatedEvent->banner_image ?: '/images/event-placeholder.jpg' }}" 
                         alt="{{ $relatedEvent->title }}" 
                         class="w-full h-32 object-cover">
                    
                    <div class="p-4">
                        <h3 class="font-bold text-gray-900 mb-2 line-clamp-1">{{ $relatedEvent->title }}</h3>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $relatedEvent->short_description }}</p>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-bold text-blue-600">
                                ₹{{ $relatedEvent->ticketTypes && $relatedEvent->ticketTypes->count() > 0 ? number_format($relatedEvent->ticketTypes->min('price')) : '0' }}
                            </span>
                            <a href="{{ route('public.event', $relatedEvent->id) }}" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded text-sm font-semibold transition-colors">
                                View
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif
</div>
@endsection
