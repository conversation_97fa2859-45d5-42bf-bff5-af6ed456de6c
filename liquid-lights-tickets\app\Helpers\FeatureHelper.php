<?php

namespace App\Helpers;

use App\Features;

class FeatureHelper
{
    /**
     * Check if a feature is enabled
     */
    public static function enabled(string $feature): bool
    {
        return Features::enabled($feature);
    }

    /**
     * Check if a feature is disabled
     */
    public static function disabled(string $feature): bool
    {
        return !Features::enabled($feature);
    }

    /**
     * Execute code only if feature is enabled
     */
    public static function when(string $feature, callable $callback, callable $fallback = null)
    {
        if (self::enabled($feature)) {
            return $callback();
        }

        if ($fallback) {
            return $fallback();
        }

        return null;
    }

    /**
     * Execute code only if feature is disabled
     */
    public static function unless(string $feature, callable $callback, callable $fallback = null)
    {
        if (self::disabled($feature)) {
            return $callback();
        }

        if ($fallback) {
            return $fallback();
        }

        return null;
    }

    /**
     * Get feature value with default
     */
    public static function value(string $feature, $default = false)
    {
        return self::enabled($feature) ? true : $default;
    }

    /**
     * Check multiple features (all must be enabled)
     */
    public static function allEnabled(array $features): bool
    {
        foreach ($features as $feature) {
            if (!self::enabled($feature)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check multiple features (any can be enabled)
     */
    public static function anyEnabled(array $features): bool
    {
        foreach ($features as $feature) {
            if (self::enabled($feature)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get enabled features from a list
     */
    public static function getEnabled(array $features): array
    {
        return array_filter($features, function ($feature) {
            return self::enabled($feature);
        });
    }

    /**
     * Get disabled features from a list
     */
    public static function getDisabled(array $features): array
    {
        return array_filter($features, function ($feature) {
            return self::disabled($feature);
        });
    }
}
