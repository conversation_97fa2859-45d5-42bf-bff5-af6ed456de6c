<?php

namespace App;

use Illuminate\Support\Facades\Cache;

class Features
{
    /**
     * Feature flags configuration
     */
    private static array $features = [
        // Event Management Features
        'advanced-event-search' => true,
        'event-recommendations' => true,
        'event-waitlist' => true,

        // Booking Features
        'group-bookings' => true,
        'seat-selection' => false,
        'booking-reminders' => true,

        // Payment Features
        'payment-plans' => false,
        'crypto-payments' => false,
        'wallet-system' => false,

        // Social Features
        'social-sharing' => true,
        'friend-invites' => true,
        'event-reviews' => true,

        // Admin Features
        'advanced-analytics' => true,
        'bulk-operations' => true,
        'automated-marketing' => false,

        // Performance Features
        'image-optimization' => true,
        'lazy-loading' => true,
        'caching-enhanced' => true,

        // Mobile Features
        'mobile-app-integration' => false,
        'push-notifications' => true,
        'offline-mode' => false,

        // Beta Features
        'ai-chatbot' => false,
        'virtual-events' => false,
        'nft-tickets' => false,

        // Seasonal Features
        'holiday-themes' => false,
        'summer-promotions' => false,

        // Regional Features
        'multi-language' => false,
        'multi-currency' => false,
        'regional-events' => true,
    ];

    /**
     * Initialize feature flags (placeholder for future Pennant integration)
     */
    public static function define(): void
    {
        // This method is ready for Laravel Pennant integration
        // For now, we use the static configuration above
    }

    /**
     * Check if a feature is enabled
     */
    public static function enabled(string $feature): bool
    {
        // Check cache first
        $cacheKey = "feature_flag_{$feature}";

        return Cache::remember($cacheKey, 3600, function () use ($feature) {
            // Check database override first
            $dbFeature = \App\Models\Feature::where('name', $feature)->first();
            if ($dbFeature) {
                return $dbFeature->value['enabled'] ?? false;
            }

            // Fall back to static configuration
            return self::$features[$feature] ?? false;
        });
    }

    /**
     * Enable a feature
     */
    public static function enable(string $feature): void
    {
        \App\Models\Feature::updateOrCreate(
            ['name' => $feature, 'scope' => 'global'],
            ['value' => ['enabled' => true]]
        );

        Cache::forget("feature_flag_{$feature}");
    }

    /**
     * Disable a feature
     */
    public static function disable(string $feature): void
    {
        \App\Models\Feature::updateOrCreate(
            ['name' => $feature, 'scope' => 'global'],
            ['value' => ['enabled' => false]]
        );

        Cache::forget("feature_flag_{$feature}");
    }

    /**
     * Get all available features with their descriptions
     */
    public static function getAllFeatures(): array
    {
        return [
            'Event Management' => [
                'advanced-event-search' => 'Advanced search with filters and sorting',
                'event-recommendations' => 'AI-powered event recommendations',
                'event-waitlist' => 'Waitlist functionality for sold-out events',
            ],
            'Booking Features' => [
                'group-bookings' => 'Group booking discounts and management',
                'seat-selection' => 'Interactive seat selection for venues',
                'booking-reminders' => 'Automated booking reminder notifications',
            ],
            'Payment Features' => [
                'payment-plans' => 'Installment payment plans',
                'crypto-payments' => 'Cryptocurrency payment support',
                'wallet-system' => 'Digital wallet system',
            ],
            'Social Features' => [
                'social-sharing' => 'Social media sharing integration',
                'friend-invites' => 'Friend invitation system',
                'event-reviews' => 'Event reviews and ratings',
            ],
            'Admin Features' => [
                'advanced-analytics' => 'Advanced analytics dashboard',
                'bulk-operations' => 'Bulk operations in admin panel',
                'automated-marketing' => 'Automated marketing campaigns',
            ],
            'Performance Features' => [
                'image-optimization' => 'Automatic image optimization',
                'lazy-loading' => 'Lazy loading for improved performance',
                'caching-enhanced' => 'Enhanced caching strategies',
            ],
            'Mobile Features' => [
                'mobile-app-integration' => 'Mobile app integration',
                'push-notifications' => 'Push notification system',
                'offline-mode' => 'Offline mode for mobile users',
            ],
            'Beta Features' => [
                'ai-chatbot' => 'AI-powered customer support chatbot',
                'virtual-events' => 'Virtual and hybrid event support',
                'nft-tickets' => 'NFT-based ticket system',
            ],
            'Regional Features' => [
                'multi-language' => 'Multi-language support',
                'multi-currency' => 'Multi-currency support',
                'regional-events' => 'Region-specific event filtering',
            ],
        ];
    }

    /**
     * Get feature status for admin dashboard
     */
    public static function getFeatureStatus(): array
    {
        $features = self::getAllFeatures();
        $status = [];

        foreach ($features as $category => $categoryFeatures) {
            $status[$category] = [];
            foreach ($categoryFeatures as $feature => $description) {
                $status[$category][$feature] = [
                    'description' => $description,
                    'enabled' => self::enabled($feature),
                ];
            }
        }

        return $status;
    }

    /**
     * Clear all feature flag cache
     */
    public static function clearCache(): void
    {
        foreach (array_keys(self::$features) as $feature) {
            Cache::forget("feature_flag_{$feature}");
        }
    }
}
