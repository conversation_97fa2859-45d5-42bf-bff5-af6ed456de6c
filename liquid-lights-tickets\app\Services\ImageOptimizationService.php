<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use App\Features;

class ImageOptimizationService
{
    protected array $sizes = [
        'thumbnail' => ['width' => 300, 'height' => 200],
        'medium' => ['width' => 600, 'height' => 400],
        'large' => ['width' => 1200, 'height' => 800],
        'hero' => ['width' => 1920, 'height' => 1080],
    ];

    protected array $allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif'
    ];

    /**
     * Optimize and store uploaded image
     */
    public function optimizeAndStore(UploadedFile $file, string $directory = 'images', array $customSizes = []): array
    {
        if (!Features::enabled('image-optimization')) {
            // If optimization is disabled, just store the original
            $path = $file->store($directory, 'public');
            return ['original' => $path];
        }

        // Validate file type
        if (!in_array($file->getMimeType(), $this->allowedMimeTypes)) {
            throw new \InvalidArgumentException('Unsupported image type');
        }

        $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
        $sizes = array_merge($this->sizes, $customSizes);
        $optimizedImages = [];

        // Store original
        $originalPath = $directory . '/original/' . $filename;
        Storage::disk('public')->put($originalPath, file_get_contents($file->getRealPath()));
        $optimizedImages['original'] = $originalPath;

        // Create optimized versions
        foreach ($sizes as $sizeName => $dimensions) {
            $optimizedPath = $this->createOptimizedVersion(
                $file->getRealPath(),
                $directory,
                $filename,
                $sizeName,
                $dimensions
            );
            $optimizedImages[$sizeName] = $optimizedPath;
        }

        // Create WebP versions if supported
        if (Features::enabled('webp-conversion')) {
            foreach ($sizes as $sizeName => $dimensions) {
                $webpPath = $this->createWebPVersion(
                    $file->getRealPath(),
                    $directory,
                    $filename,
                    $sizeName,
                    $dimensions
                );
                $optimizedImages[$sizeName . '_webp'] = $webpPath;
            }
        }

        return $optimizedImages;
    }

    /**
     * Create optimized version of image
     */
    protected function createOptimizedVersion(string $sourcePath, string $directory, string $filename, string $sizeName, array $dimensions): string
    {
        $image = Image::make($sourcePath);
        
        // Resize image
        $image->fit($dimensions['width'], $dimensions['height'], function ($constraint) {
            $constraint->upsize();
        });

        // Optimize quality
        $quality = $this->getOptimizationQuality($sizeName);
        
        // Generate filename
        $optimizedFilename = pathinfo($filename, PATHINFO_FILENAME) . "_{$sizeName}." . pathinfo($filename, PATHINFO_EXTENSION);
        $optimizedPath = $directory . '/' . $sizeName . '/' . $optimizedFilename;

        // Save optimized image
        $imageData = $image->encode(null, $quality)->__toString();
        Storage::disk('public')->put($optimizedPath, $imageData);

        return $optimizedPath;
    }

    /**
     * Create WebP version of image
     */
    protected function createWebPVersion(string $sourcePath, string $directory, string $filename, string $sizeName, array $dimensions): string
    {
        $image = Image::make($sourcePath);
        
        // Resize image
        $image->fit($dimensions['width'], $dimensions['height'], function ($constraint) {
            $constraint->upsize();
        });

        // Generate WebP filename
        $webpFilename = pathinfo($filename, PATHINFO_FILENAME) . "_{$sizeName}.webp";
        $webpPath = $directory . '/webp/' . $webpFilename;

        // Save as WebP with high quality
        $imageData = $image->encode('webp', 85)->__toString();
        Storage::disk('public')->put($webpPath, $imageData);

        return $webpPath;
    }

    /**
     * Get optimization quality based on size
     */
    protected function getOptimizationQuality(string $sizeName): int
    {
        return match($sizeName) {
            'thumbnail' => 70,
            'medium' => 80,
            'large' => 85,
            'hero' => 90,
            default => 80
        };
    }

    /**
     * Generate responsive image HTML
     */
    public function generateResponsiveImageHtml(array $imagePaths, string $alt = '', array $attributes = []): string
    {
        if (empty($imagePaths)) {
            return '';
        }

        $baseAttributes = array_merge([
            'alt' => $alt,
            'loading' => Features::enabled('lazy-loading') ? 'lazy' : 'eager',
            'decoding' => 'async',
        ], $attributes);

        // Build srcset for different sizes
        $srcset = [];
        $webpSrcset = [];

        foreach (['thumbnail', 'medium', 'large', 'hero'] as $size) {
            if (isset($imagePaths[$size])) {
                $width = $this->sizes[$size]['width'];
                $srcset[] = Storage::url($imagePaths[$size]) . " {$width}w";
                
                if (isset($imagePaths[$size . '_webp'])) {
                    $webpSrcset[] = Storage::url($imagePaths[$size . '_webp']) . " {$width}w";
                }
            }
        }

        $html = '<picture>';

        // Add WebP source if available
        if (!empty($webpSrcset)) {
            $html .= '<source type="image/webp" srcset="' . implode(', ', $webpSrcset) . '" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw">';
        }

        // Add fallback source
        if (!empty($srcset)) {
            $html .= '<source srcset="' . implode(', ', $srcset) . '" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw">';
        }

        // Add img tag
        $src = isset($imagePaths['medium']) ? Storage::url($imagePaths['medium']) : Storage::url($imagePaths['original']);
        $attributeString = '';
        foreach ($baseAttributes as $key => $value) {
            $attributeString .= " {$key}=\"" . htmlspecialchars($value) . "\"";
        }

        $html .= "<img src=\"{$src}\"{$attributeString}>";
        $html .= '</picture>';

        return $html;
    }

    /**
     * Clean up old image versions
     */
    public function cleanupOldVersions(array $imagePaths): void
    {
        foreach ($imagePaths as $path) {
            if ($path && Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
            }
        }
    }

    /**
     * Get image info
     */
    public function getImageInfo(string $path): array
    {
        if (!Storage::disk('public')->exists($path)) {
            return [];
        }

        $fullPath = Storage::disk('public')->path($path);
        $imageInfo = getimagesize($fullPath);

        return [
            'width' => $imageInfo[0] ?? 0,
            'height' => $imageInfo[1] ?? 0,
            'mime_type' => $imageInfo['mime'] ?? '',
            'size' => Storage::disk('public')->size($path),
            'url' => Storage::url($path),
        ];
    }

    /**
     * Compress existing image
     */
    public function compressExistingImage(string $path, int $quality = 80): string
    {
        if (!Storage::disk('public')->exists($path)) {
            throw new \InvalidArgumentException('Image not found');
        }

        $fullPath = Storage::disk('public')->path($path);
        $image = Image::make($fullPath);
        
        // Compress and save
        $compressedData = $image->encode(null, $quality)->__toString();
        Storage::disk('public')->put($path, $compressedData);

        return $path;
    }

    /**
     * Batch optimize images
     */
    public function batchOptimize(array $imagePaths, int $quality = 80): array
    {
        $results = [];

        foreach ($imagePaths as $path) {
            try {
                $this->compressExistingImage($path, $quality);
                $results[$path] = ['status' => 'success', 'message' => 'Optimized successfully'];
            } catch (\Exception $e) {
                $results[$path] = ['status' => 'error', 'message' => $e->getMessage()];
            }
        }

        return $results;
    }

    /**
     * Generate placeholder image
     */
    public function generatePlaceholder(int $width = 300, int $height = 200, string $color = '#f3f4f6'): string
    {
        $image = Image::canvas($width, $height, $color);
        
        // Add text
        $image->text("{$width}x{$height}", $width/2, $height/2, function($font) {
            $font->size(16);
            $font->color('#9ca3af');
            $font->align('center');
            $font->valign('middle');
        });

        $filename = "placeholder_{$width}x{$height}.png";
        $path = "placeholders/{$filename}";
        
        Storage::disk('public')->put($path, $image->encode('png')->__toString());

        return $path;
    }
}
