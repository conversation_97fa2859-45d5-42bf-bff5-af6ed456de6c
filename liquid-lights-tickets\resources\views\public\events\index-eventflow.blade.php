@extends('public.layout')

@section('content')
<!-- <PERSON> Header -->
<section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16 lg:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center fade-in">
            <h1 class="heading-lg text-gray-900 mb-6">
                Discover <span class="text-gradient">Amazing Events</span>
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                From concerts and festivals to workshops and networking events, find your next unforgettable experience.
            </p>

            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <form method="GET" action="{{ route('public.events') }}" class="relative">
                    <div class="flex">
                        <input type="text" 
                               name="search" 
                               value="{{ request('search') }}"
                               placeholder="Search events, artists, venues..."
                               class="modern-input rounded-r-none flex-1">
                        <button type="submit" 
                                class="btn-primary rounded-l-none px-6">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="bg-white border-b border-gray-100 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <!-- Filter Buttons -->
            <div class="flex flex-wrap gap-3">
                <button class="filter-btn active" data-filter="all">
                    All Events
                </button>
                <button class="filter-btn" data-filter="music">
                    Music
                </button>
                <button class="filter-btn" data-filter="nightlife">
                    Nightlife
                </button>
                <button class="filter-btn" data-filter="festival">
                    Festivals
                </button>
                <button class="filter-btn" data-filter="workshop">
                    Workshops
                </button>
            </div>

            <!-- Sort Dropdown -->
            <div class="relative">
                <select class="modern-input pr-10 min-w-[200px]" onchange="sortEvents(this.value)">
                    <option value="date">Sort by Date</option>
                    <option value="price_low">Price: Low to High</option>
                    <option value="price_high">Price: High to Low</option>
                    <option value="popularity">Most Popular</option>
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Events Grid -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(isset($events) && $events->count() > 0)
            <div id="events-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($events as $index => $event)
                    <div class="event-card modern-card overflow-hidden slide-up" 
                         style="animation-delay: {{ ($index % 12) * 0.05 }}s;"
                         data-category="{{ strtolower($event->category ?? 'general') }}"
                         data-price="{{ $event->ticketTypes ? $event->ticketTypes->min('price') : 0 }}"
                         data-date="{{ $event->event_date }}">
                        
                        <!-- Event Image -->
                        <div class="relative h-48 overflow-hidden">
                            @if($event->featured_image)
                                <img src="{{ Storage::url($event->featured_image) }}" 
                                     alt="{{ $event->title }}"
                                     class="w-full h-full object-cover transition-transform duration-500 hover:scale-110">
                            @else
                                <div class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Event Date Badge -->
                            <div class="absolute top-3 left-3 bg-white/95 backdrop-blur-sm px-3 py-1 rounded-lg">
                                <div class="text-xs font-bold text-gray-900">
                                    {{ \Carbon\Carbon::parse($event->event_date)->format('M') }}
                                </div>
                                <div class="text-lg font-bold text-gray-900 leading-none">
                                    {{ \Carbon\Carbon::parse($event->event_date)->format('d') }}
                                </div>
                            </div>

                            <!-- Price Badge -->
                            @if($event->ticketTypes && $event->ticketTypes->count() > 0)
                                <div class="absolute top-3 right-3 bg-gradient-to-r from-green-500 to-blue-500 text-white px-3 py-1 rounded-lg">
                                    <div class="text-sm font-bold">
                                        ₹{{ number_format($event->ticketTypes->min('price')) }}
                                        @if($event->ticketTypes->min('price') != $event->ticketTypes->max('price'))
                                            +
                                        @endif
                                    </div>
                                </div>
                            @endif

                            <!-- Category Badge -->
                            @if($event->category)
                                <div class="absolute bottom-3 left-3 badge badge-primary">
                                    {{ $event->category }}
                                </div>
                            @endif
                        </div>

                        <!-- Event Details -->
                        <div class="p-5">
                            <h3 class="font-bold text-gray-900 mb-2 text-lg leading-tight hover:text-blue-600 transition-colors">
                                <a href="{{ route('public.events.show', $event->slug) }}">
                                    {{ $event->title }}
                                </a>
                            </h3>
                            
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                {{ Str::limit(strip_tags($event->description), 80) }}
                            </p>

                            <!-- Event Meta -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="truncate">{{ \Carbon\Carbon::parse($event->event_date)->format('M d, Y') }} • {{ $event->event_time }}</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2 text-purple-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <span class="truncate">{{ $event->venue_name }}</span>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <a href="{{ route('public.events.show', $event->slug) }}" 
                               class="btn-primary w-full justify-center text-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                                </svg>
                                View Details
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if(method_exists($events, 'links'))
                <div class="mt-12 flex justify-center fade-in">
                    {{ $events->links('pagination::tailwind') }}
                </div>
            @endif

        @else
            <!-- No Events State -->
            <div class="text-center py-16 fade-in">
                <div class="modern-card p-12 max-w-md mx-auto">
                    <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">No Events Found</h3>
                    <p class="text-gray-600 mb-8">
                        @if(request('search'))
                            No events match your search criteria. Try adjusting your search terms.
                        @else
                            No events are currently available. Check back soon for exciting new events!
                        @endif
                    </p>
                    @if(request('search'))
                        <a href="{{ route('public.events') }}" class="btn-primary">
                            View All Events
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>
</section>

<style>
    .filter-btn {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        border: 2px solid var(--gray-200);
        background: white;
        color: var(--gray-600);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .filter-btn:hover {
        border-color: var(--primary-300);
        color: var(--primary-600);
    }

    .filter-btn.active {
        background: var(--gradient-primary);
        border-color: transparent;
        color: white;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

<script>
    // Filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        const eventCards = document.querySelectorAll('.event-card');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                const filter = this.dataset.filter;

                // Filter events
                eventCards.forEach(card => {
                    if (filter === 'all' || card.dataset.category === filter) {
                        card.style.display = 'block';
                        card.classList.add('slide-up');
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    });

    // Sort functionality
    function sortEvents(sortBy) {
        const grid = document.getElementById('events-grid');
        const cards = Array.from(grid.children);

        cards.sort((a, b) => {
            switch(sortBy) {
                case 'price_low':
                    return parseInt(a.dataset.price) - parseInt(b.dataset.price);
                case 'price_high':
                    return parseInt(b.dataset.price) - parseInt(a.dataset.price);
                case 'date':
                    return new Date(a.dataset.date) - new Date(b.dataset.date);
                default:
                    return 0;
            }
        });

        // Re-append sorted cards
        cards.forEach(card => grid.appendChild(card));
    }
</script>
@endsection
